// lib/services/admin.service.ts
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { AdminDashboard, UserManagementFilters } from '@/types/admin.types'
import { AppError } from '@/lib/errors/error-types'

// Type for MongoDB query objects
interface UserQuery {
  role?: string
  isActive?: boolean
  isEmailVerified?: boolean
  createdAt?: {
    $gte?: Date
    $lte?: Date
  }
  $or?: Array<{
    'profile.firstName'?: { $regex: string; $options: string }
    'profile.lastName'?: { $regex: string; $options: string }
    email?: { $regex: string; $options: string }
  }>
}

interface CompanyQuery {
  isVerified?: boolean
  isActive?: boolean
  'verification.isVerified'?: boolean
  createdAt?: {
    $gte?: Date
    $lte?: Date
  }
  $or?: Array<{
    name?: { $regex: string; $options: string }
    description?: { $regex: string; $options: string }
    slug?: { $regex: string; $options: string }
  }>
}

// Helper function to check if error is an AppError
function isAppError(error: unknown): error is AppError {
  return error instanceof AppError
}

export class AdminService {
  /**
   * Get admin dashboard data
   */
  async getDashboardData(): Promise<AdminDashboard> {
    try {
      const [
        totalUsers,
        totalCompanies,
        totalJobs,
        totalApplications,
        activeUsers,
        newUsersToday,
        newCompaniesToday,
        newJobsToday
      ] = await Promise.all([
        User.countDocuments(),
        Company.countDocuments(),
        Job.countDocuments(),
        Application.countDocuments(),
        User.countDocuments({ isActive: true }),
        this.getNewUsersToday(),
        this.getNewCompaniesToday(),
        this.getNewJobsToday()
      ])

      const stats = {
        totalUsers,
        totalCompanies,
        totalJobs,
        totalApplications,
        activeUsers,
        newUsersToday,
        newCompaniesToday,
        newJobsToday,
        pendingReports: 0, // TODO: Implement reports system
        pendingVerifications: await Company.countDocuments({ 'verification.isVerified': false })
      }

      const charts = {
        userGrowth: await this.getUserGrowthData(),
        jobPostings: await this.getJobPostingsData(),
        applications: await this.getApplicationsData(),
        revenue: await this.getRevenueData()
      }

      const recentActivity = await this.getRecentActivity()
      const alerts = await this.getSystemAlerts()

      return {
        stats,
        charts,
        recentActivity,
        alerts
      }
    } catch {
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to fetch dashboard data',
        'dashboard'
      )
    }
  }

  /**
   * Get users with pagination and filters
   */
  async getUsers(
    page: number = 1,
    limit: number = 20,
    filters: UserManagementFilters = {}
  ) {
    try {
      const skip = (page - 1) * limit
      const query: UserQuery = {}

      // Apply filters
      if (filters.role) {
        query.role = filters.role
      }
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive
      }
      if (filters.isEmailVerified !== undefined) {
        query.isEmailVerified = filters.isEmailVerified
      }
      if (filters.search) {
        query.$or = [
          { email: { $regex: filters.search, $options: 'i' } },
          { 'profile.firstName': { $regex: filters.search, $options: 'i' } },
          { 'profile.lastName': { $regex: filters.search, $options: 'i' } }
        ]
      }
      if (filters.dateFrom || filters.dateTo) {
        query.createdAt = {}
        if (filters.dateFrom) query.createdAt.$gte = new Date(filters.dateFrom)
        if (filters.dateTo) query.createdAt.$lte = new Date(filters.dateTo)
      }

      const [users, total] = await Promise.all([
        User.find(query)
          .select('-password')
          .populate('companyId', 'name slug')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments(query)
      ])

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch {
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to fetch users',
        'users'
      )
    }
  }

  /**
   * Update user status (activate/deactivate)
   */
  async updateUserStatus(userId: string, isActive: boolean): Promise<void> {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'User not found',
          'user'
        )
      }

      user.isActive = isActive
      await user.save()
    } catch (error) {
      if (isAppError(error) && error.code === ErrorCode.NOT_FOUND) throw error
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to update user status',
        'user'
      )
    }
  }

  /**
   * Update user role
   */
  async updateUserRole(userId: string, newRole: string): Promise<void> {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'User not found',
          'user'
        )
      }

      const validRoles = ['admin', 'company_admin', 'recruiter', 'job_seeker']
      if (!validRoles.includes(newRole)) {
        throw errorService.createError(
          ErrorCode.VALIDATION_ERROR,
          'Invalid role specified',
          'role'
        )
      }

      user.role = newRole as 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
      await user.save()
    } catch (error) {
      if (isAppError(error) && (error.code === ErrorCode.NOT_FOUND || error.code === ErrorCode.VALIDATION_ERROR)) {
        throw error
      }
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to update user role',
        'user'
      )
    }
  }

  /**
   * Delete user account (soft delete)
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId)
      if (!user) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'User not found',
          'user'
        )
      }

      // Soft delete by deactivating
      user.isActive = false
      await user.save()

      // TODO: Handle cleanup of related data (applications, jobs, etc.)
    } catch (error) {
      if (isAppError(error) && error.code === ErrorCode.NOT_FOUND) throw error
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to delete user',
        'user'
      )
    }
  }

  /**
   * Get companies with verification status
   */
  async getCompanies(
    page: number = 1,
    limit: number = 20,
    filters: { verificationStatus?: string; search?: string } = {}
  ) {
    try {
      const skip = (page - 1) * limit
      const query: CompanyQuery = {}

      if (filters.verificationStatus) {
        if (filters.verificationStatus === 'verified') {
          query['verification.isVerified'] = true
        } else if (filters.verificationStatus === 'pending') {
          query['verification.isVerified'] = false
        }
      }
      if (filters.search) {
        query.$or = [
          { name: { $regex: filters.search, $options: 'i' } },
          { slug: { $regex: filters.search, $options: 'i' } }
        ]
      }

      const [companies, total] = await Promise.all([
        Company.find(query)
          .populate('admins', 'profile.firstName profile.lastName email')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Company.countDocuments(query)
      ])

      return {
        companies,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch {
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to fetch companies',
        'companies'
      )
    }
  }

  /**
   * Verify company
   */
  async verifyCompany(companyId: string, verified: boolean, notes?: string): Promise<void> {
    try {
      const company = await Company.findById(companyId)
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'company'
        )
      }

      company.verification.isVerified = verified
      if (notes) {
        // Add notes to a notes field if it exists, or create a custom field
        company.verification.notes = notes
      }
      company.verification.verifiedAt = verified ? new Date() : undefined

      await company.save()

      // TODO: Send notification to company admin
    } catch (error) {
      if (isAppError(error) && error.code === ErrorCode.NOT_FOUND) throw error
      throw errorService.createError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Failed to verify company',
        'company'
      )
    }
  }

  // Helper methods
  private async getNewUsersToday(): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return User.countDocuments({ createdAt: { $gte: today } })
  }

  private async getNewCompaniesToday(): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return Company.countDocuments({ createdAt: { $gte: today } })
  }

  private async getNewJobsToday(): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return Job.countDocuments({ createdAt: { $gte: today } })
  }

  private async getUserGrowthData() {
    // TODO: Implement user growth chart data
    return []
  }

  private async getJobPostingsData() {
    // TODO: Implement job postings chart data
    return []
  }

  private async getApplicationsData() {
    // TODO: Implement applications chart data
    return []
  }

  private async getRevenueData() {
    // TODO: Implement revenue chart data
    return []
  }

  private async getRecentActivity() {
    // TODO: Implement recent activity feed
    return []
  }

  private async getSystemAlerts() {
    // TODO: Implement system alerts
    return []
  }
}

export const adminService = new AdminService()
