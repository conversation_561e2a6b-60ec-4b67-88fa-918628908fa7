// lib/services/client.service.ts
import { Client } from '@/lib/models/client.model'
// Remove unused import
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { cacheService } from '@/lib/services/cache.service'
import { 
  CreateClientRequest, 
  UpdateClientRequest, 
  ClientProfile, 
  ClientSearchFilters, 
  ClientSearchResult 
} from '@/types/client.types'

export class ClientService {
  /**
   * Create a new client profile
   */
  async createClient(clientData: CreateClientRequest, userId: string): Promise<ClientProfile> {
    try {
      // Check if client already exists for this user
      const existingClient = await Client.findOne({ user: userId })
      
      if (existingClient) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'Client profile already exists for this user',
          'user'
        )
      }
      
      // Create client
      const client = new Client({
        ...clientData,
        user: userId,
        isActive: true,
        isPublic: true
      })
      
      await client.save()
      
      // Populate user data
      await client.populate('user', 'profile preferences')
      
      return this.formatClientProfile(client)
      
    } catch (error: unknown) {
      if (typeof error === 'object' && error !== null && 'code' in error && (error as Record<string, unknown>).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'Client profile already exists for this user',
          'user'
        )
      }
      throw error
    }
  }

  /**
   * Get client by ID
   */
  async getClientById(clientId: string): Promise<ClientProfile> {
    const client = await Client.findById(clientId)
      .populate('user', 'profile preferences')
    
    if (!client) {
      throw errorService.createError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Client not found',
        'clientId'
      )
    }
    
    return this.formatClientProfile(client)
  }

  /**
   * Get client by user ID
   */
  async getClientByUserId(userId: string): Promise<ClientProfile> {
    const client = await Client.findOne({ user: userId })
      .populate('user', 'profile preferences')
    
    if (!client) {
      throw errorService.createError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Client profile not found for this user',
        'userId'
      )
    }
    
    return this.formatClientProfile(client)
  }

  /**
   * Update client profile
   */
  async updateClient(clientId: string, updateData: UpdateClientRequest): Promise<ClientProfile> {
    const client = await Client.findById(clientId)

    if (!client) {
      throw errorService.createError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Client not found',
        'clientId'
      )
    }

    // Update fields
    Object.assign(client, updateData)

    // Update last profile update timestamp
    client.activity.lastProfileUpdate = new Date()

    await client.save()

    // Populate user data
    await client.populate('user', 'profile preferences')

    // Clear cache
    await this.clearClientCache(clientId)

    return this.formatClientProfile(client)
  }

  /**
   * Update client by user ID
   */
  async updateClientByUserId(userId: string, updateData: UpdateClientRequest): Promise<ClientProfile> {
    const client = await Client.findOne({ user: userId })

    if (!client) {
      throw errorService.createError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Client profile not found for this user',
        'userId'
      )
    }

    return this.updateClient(client._id.toString(), updateData)
  }

  /**
   * Update client by ID
   */
  async updateClientById(clientId: string, updateData: UpdateClientRequest): Promise<ClientProfile> {
    return this.updateClient(clientId, updateData)
  }

  /**
   * Search clients with filters
   */
  async searchClients(
    filters: ClientSearchFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<ClientSearchResult> {
    const validPage = Math.max(1, page)
    const validLimit = Math.min(Math.max(1, limit), 100)
    const skip = (validPage - 1) * validLimit

    // Build query
    const query: Record<string, unknown> = {
      isActive: true,
      'privacy.profileVisibility': { $in: ['public', 'recruiters_only'] }
    }

    if (filters.skills && filters.skills.length > 0) {
      query['skills.name'] = { $in: filters.skills }
    }

    if (filters.experience && filters.experience.length > 0) {
      query['experience.level'] = { $in: filters.experience }
    }

    if (filters.industries && filters.industries.length > 0) {
      query['experience.industries'] = { $in: filters.industries }
    }

    if (filters.locations && filters.locations.length > 0) {
      query['jobPreferences.locations.city'] = { $in: filters.locations }
    }

    if (filters.availability && filters.availability.length > 0) {
      query['jobPreferences.availability'] = { $in: filters.availability }
    }

    if (filters.salaryMin || filters.salaryMax) {
      const salaryQuery: Record<string, unknown> = {}
      if (filters.salaryMin) {
        salaryQuery['jobPreferences.salaryExpectation.min'] = { $gte: filters.salaryMin }
      }
      if (filters.salaryMax) {
        salaryQuery['jobPreferences.salaryExpectation.max'] = { $lte: filters.salaryMax }
      }
      Object.assign(query, salaryQuery)
    }

    // Execute query
    const [clients, total] = await Promise.all([
      Client.find(query)
        .populate('user', 'profile preferences')
        .sort({ 'activity.lastProfileUpdate': -1 })
        .skip(skip)
        .limit(validLimit),
      Client.countDocuments(query)
    ])

    return {
      clients: clients.map(client => this.formatClientProfile(client)),
      total,
      page: validPage,
      limit: validLimit,
      hasMore: skip + clients.length < total
    }
  }

  /**
   * Update client activity stats
   */
  async updateClientActivity(clientId: string, activityType: 'view' | 'search' | 'recruiter_view'): Promise<void> {
    const updateField = activityType === 'view' ? 'activity.profileViews' :
                       activityType === 'search' ? 'activity.searchAppearances' :
                       'activity.recruiterViews'

    await Client.findByIdAndUpdate(clientId, {
      $inc: { [updateField]: 1 }
    })
  }

  /**
   * Add job to saved jobs
   */
  async saveJob(clientId: string, jobId: string): Promise<void> {
    await Client.findByIdAndUpdate(clientId, {
      $addToSet: { savedJobs: jobId }
    })
  }

  /**
   * Remove job from saved jobs
   */
  async unsaveJob(clientId: string, jobId: string): Promise<void> {
    await Client.findByIdAndUpdate(clientId, {
      $pull: { savedJobs: jobId }
    })
  }

  /**
   * Follow company
   */
  async followCompany(clientId: string, companyId: string): Promise<void> {
    await Client.findByIdAndUpdate(clientId, {
      $addToSet: { followedCompanies: companyId }
    })
  }

  /**
   * Unfollow company
   */
  async unfollowCompany(clientId: string, companyId: string): Promise<void> {
    await Client.findByIdAndUpdate(clientId, {
      $pull: { followedCompanies: companyId }
    })
  }

  /**
   * Delete client profile
   */
  async deleteClient(clientId: string): Promise<void> {
    const client = await Client.findById(clientId)
    
    if (!client) {
      throw errorService.createError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Client not found',
        'clientId'
      )
    }
    
    await Client.findByIdAndDelete(clientId)
    
    // Clear cache
    await this.clearClientCache(clientId)
  }

  /**
   * Format client profile for response
   */
  private formatClientProfile(client: Record<string, unknown>): ClientProfile {
    // Type assertion for client object with toObject method
    const clientWithMethods = client as { toObject: () => Record<string, unknown> }
    const clientObj = clientWithMethods.toObject()

    // Type assertions for nested properties
    const user = client.user as { profile?: { firstName?: string; lastName?: string } } | undefined
    const activity = client.activity as { profileCompleteness?: number } | undefined

    return {
      ...clientObj,
      fullName: user?.profile ?
        `${user.profile.firstName || ''} ${user.profile.lastName || ''}`.trim() :
        'Unknown User',
      profileCompleteness: activity?.profileCompleteness || 0
    } as ClientProfile
  }

  /**
   * Clear client cache
   */
  private async clearClientCache(clientId: string): Promise<void> {
    try {
      const patterns = [
        `client:${clientId}*`,
        'clients:search:*'
      ]

      for (const pattern of patterns) {
        const keys = await cacheService.keys(pattern)
        for (const key of keys) {
          await cacheService.del(key)
        }
      }
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }
}

// Export singleton instance
export const clientService = new ClientService()
