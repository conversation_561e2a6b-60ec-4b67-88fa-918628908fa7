// lib/services/validation.service.ts
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { LoginRequest, RegisterRequest } from './auth.service'

export class ValidationService {
  /**
   * Validate login request data
   */
  validateLoginRequest(data: unknown): LoginRequest {
    const errors: string[] = []

    // Type guard to ensure data is an object
    if (typeof data !== 'object' || data === null) {
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
    }

    const requestData = data as Record<string, unknown>

    if (!requestData.email) errors.push('Email is required')
    if (!requestData.password) errors.push('Password is required')
    
    if (requestData.email && typeof requestData.email === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      errors.push('Invalid email format')
    }
    
    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }
    
    return {
      email: String(requestData.email).toLowerCase().trim(),
      password: String(requestData.password),
      rememberMe: Boolean(requestData.rememberMe)
    }
  }

  /**
   * Validate registration request data
   */
  validateRegisterRequest(data: unknown): RegisterRequest {
    const errors: string[] = []

    // Type guard to ensure data is an object
    if (typeof data !== 'object' || data === null) {
      throw errorService.createError(ErrorCode.VALIDATION_ERROR, 'Invalid request data')
    }

    const requestData = data as Record<string, unknown>

    // Required fields validation
    if (!requestData.email) errors.push('Email is required')
    if (!requestData.password) errors.push('Password is required')
    if (!requestData.firstName) errors.push('First name is required')
    if (!requestData.lastName) errors.push('Last name is required')

    // Email format validation
    if (requestData.email && typeof requestData.email === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      errors.push('Invalid email format')
    }

    // Password strength validation
    if (requestData.password && typeof requestData.password === 'string' && requestData.password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }

    // Role validation
    if (requestData.role && typeof requestData.role === 'string' && !['job_seeker', 'company_admin'].includes(requestData.role)) {
      errors.push('Invalid role. Must be job_seeker or company_admin')
    }

    // Phone validation (if provided)
    if (requestData.phone && typeof requestData.phone === 'string' && !/^\+?[\d\s\-\(\)]+$/.test(requestData.phone)) {
      errors.push('Invalid phone number format')
    }
    
    if (errors.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Validation failed: ${errors.join(', ')}`,
        undefined,
        { validationErrors: errors }
      )
    }
    
    return {
      email: String(requestData.email).toLowerCase().trim(),
      password: String(requestData.password),
      firstName: String(requestData.firstName).trim(),
      lastName: String(requestData.lastName).trim(),
      role: (requestData.role as 'job_seeker' | 'company_admin') || 'job_seeker',
      phone: requestData.phone ? String(requestData.phone).trim() : undefined,
      location: requestData.location as Record<string, unknown> | undefined
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): boolean {
    return /^\+?[\d\s\-\(\)]+$/.test(phone)
  }

  /**
   * Sanitize string input
   */
  sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '')
  }

  /**
   * Validate required fields
   */
  validateRequiredFields(data: Record<string, unknown>, requiredFields: string[]): string[] {
    const errors: string[] = []
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} is required`)
      }
    })
    
    return errors
  }
}

export const validationService = new ValidationService()
