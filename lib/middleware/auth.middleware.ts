// lib/middleware/auth.middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { User } from '@/lib/models/user.model'

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string
    email: string
    role: string
  }
}

// JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// Verify JWT token
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET)
  } catch (error) {
    throw new Error('Invalid token')
  }
}

// Generate JWT token
export function generateToken(payload: any, expiresIn: string = '7d'): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn })
}

// Authentication middleware
export async function authenticateUser(request: NextRequest): Promise<{
  user: any
  error?: string
}> {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { user: null, error: 'No token provided' }
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token
    const decoded = verifyToken(token)

    // Get user from database
    const user = await User.findById(decoded.userId).select('-password')
    if (!user) {
      return { user: null, error: 'User not found' }
    }

    if (!user.isActive) {
      return { user: null, error: 'Account is deactivated' }
    }

    return { user }
  } catch (error) {
    return { user: null, error: 'Authentication failed' }
  }
}

// Auth middleware for API routes
export async function authMiddleware(request: NextRequest): Promise<{
  success: boolean
  user?: any
  error?: string
  status?: number
}> {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'No token provided',
        status: 401
      }
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token
    const decoded = verifyToken(token)

    // Get user from database
    const user = await User.findById(decoded.userId).select('-password')
    if (!user) {
      return {
        success: false,
        error: 'User not found',
        status: 401
      }
    }

    if (!user.isActive) {
      return {
        success: false,
        error: 'Account is deactivated',
        status: 401
      }
    }

    return {
      success: true,
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role
      }
    }
  } catch (error) {
    return {
      success: false,
      error: 'Authentication failed',
      status: 401
    }
  }
}

// Role-based authorization middleware
export function requireRole(allowedRoles: string[]) {
  return async (request: NextRequest, user: any) => {
    if (!user) {
      return { authorized: false, error: 'Authentication required' }
    }

    if (!allowedRoles.includes(user.role)) {
      return { authorized: false, error: 'Insufficient permissions' }
    }

    return { authorized: true }
  }
}

// Middleware wrapper for API routes
export function withAuth(
  handler: (request: NextRequest, context: { user: any }) => Promise<NextResponse>,
  options: {
    requiredRoles?: string[]
    optional?: boolean
  } = {}
) {
  return async (request: NextRequest, context: any) => {
    try {
      const { user, error } = await authenticateUser(request)

      // If authentication is optional and no user, continue without user
      if (options.optional && !user && error) {
        return handler(request, { ...context, user: null })
      }

      // If authentication failed and not optional, return error
      if (!user && error) {
        return NextResponse.json(
          { error: error || 'Authentication required' },
          { status: 401 }
        )
      }

      // Check role requirements
      if (options.requiredRoles && options.requiredRoles.length > 0) {
        const roleCheck = await requireRole(options.requiredRoles)(request, user)
        if (!roleCheck.authorized) {
          return NextResponse.json(
            { error: roleCheck.error },
            { status: 403 }
          )
        }
      }

      // Add user to context and call handler
      return handler(request, { ...context, user })
    } catch (error) {
      console.error('Auth middleware error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Extract user from request (for use in API routes)
export async function getUserFromRequest(request: NextRequest): Promise<any> {
  const { user } = await authenticateUser(request)
  return user
}

// Check if user owns resource
export function checkResourceOwnership(userId: string, resourceUserId: string): boolean {
  return userId === resourceUserId
}

// Admin check
export function isAdmin(user: any): boolean {
  return user?.role === 'admin'
}

// Super admin check (for future use)
export function isSuperAdmin(user: any): boolean {
  return user?.role === 'admin' && user?.adminRole === 'super_admin'
}

// Company admin check
export function isCompanyAdmin(user: any): boolean {
  return user?.role === 'company_admin' || user?.role === 'admin'
}

// Recruiter check
export function isRecruiter(user: any): boolean {
  return ['recruiter', 'company_admin', 'admin'].includes(user?.role)
}

// Job seeker check
export function isJobSeeker(user: any): boolean {
  return user?.role === 'job_seeker'
}

// Admin permissions check
export function hasAdminPermission(user: any, permission: string): boolean {
  if (!isAdmin(user)) return false

  // Super admin has all permissions
  if (isSuperAdmin(user)) return true

  // Check specific permissions (for future granular permissions)
  const adminPermissions = user?.permissions || []
  return adminPermissions.includes(permission) || adminPermissions.includes('*')
}

// Admin middleware wrapper
export function withAdminAuth(
  handler: (request: NextRequest, context: { user: any }) => Promise<NextResponse>,
  options: {
    requiredPermissions?: string[]
    allowSuperAdminOnly?: boolean
  } = {}
) {
  return withAuth(handler, {
    requiredRoles: ['admin'],
    ...options
  })
}
