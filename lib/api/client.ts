import { useAuthStore } from '@/stores/auth.store'

export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface RequestOptions extends RequestInit {
  requireAuth?: boolean
}

class ApiClient {
  private baseURL: string

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || ''
  }

  private getAuthHeaders(): HeadersInit {
    const token = useAuthStore.getState().token
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bear<PERSON> ${token}` })
    }
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type')
    const isJson = contentType?.includes('application/json')
    
    let data: unknown
    try {
      data = isJson ? await response.json() : await response.text()
    } catch {
      throw new Error('Failed to parse response')
    }

    if (!response.ok) {
      // Handle 401 unauthorized - token might be expired
      if (response.status === 401) {
        const authStore = useAuthStore.getState()
        // Try to refresh token if we have a refresh token
        if (authStore.refreshToken) {
          try {
            await authStore.refreshAuth()
            // Retry the original request with new token
            return this.handleResponse(response)
          } catch {
            // If refresh fails, logout user
            authStore.logout()
            throw new Error('Authentication failed. Please login again.')
          }
        } else {
          // No refresh token, logout user
          authStore.logout()
          throw new Error('Authentication required. Please login.')
        }
      }

      // Type guard for error response
      const errorData = data as { error?: string; message?: string }
      const errorMessage = errorData?.error || errorData?.message || `HTTP ${response.status}: ${response.statusText}`
      throw new Error(errorMessage)
    }

    return data as ApiResponse<T>
  }

  async get<T = unknown>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers: requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' },
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }

  async post<T = unknown>(endpoint: string, data?: unknown, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }

  async put<T = unknown>(endpoint: string, data?: unknown, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }

  async patch<T = unknown>(endpoint: string, data?: unknown, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PATCH',
      headers: requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }

  async delete<T = unknown>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' },
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }

  // Upload method for file uploads
  async upload<T = unknown>(endpoint: string, formData: FormData, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { requireAuth = true, ...fetchOptions } = options
    const token = useAuthStore.getState().token
    
    const headers: HeadersInit = {}
    if (requireAuth && token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
      ...fetchOptions
    })

    return this.handleResponse<T>(response)
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient()

// Export convenience methods
export const api = {
  get: <T = unknown>(endpoint: string, options?: RequestOptions) => apiClient.get<T>(endpoint, options),
  post: <T = unknown>(endpoint: string, data?: unknown, options?: RequestOptions) => apiClient.post<T>(endpoint, data, options),
  put: <T = unknown>(endpoint: string, data?: unknown, options?: RequestOptions) => apiClient.put<T>(endpoint, data, options),
  patch: <T = unknown>(endpoint: string, data?: unknown, options?: RequestOptions) => apiClient.patch<T>(endpoint, data, options),
  delete: <T = unknown>(endpoint: string, options?: RequestOptions) => apiClient.delete<T>(endpoint, options),
  upload: <T = unknown>(endpoint: string, formData: FormData, options?: RequestOptions) => apiClient.upload<T>(endpoint, formData, options)
}

export default apiClient
