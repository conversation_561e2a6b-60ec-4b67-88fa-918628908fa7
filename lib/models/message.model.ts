// lib/models/message.model.ts
import mongoose, { Schema, Document, Model } from 'mongoose'
import { ObjectId } from '@/types/base.types'

// Message interface
export interface IMessage extends Document {
  _id: ObjectId
  conversationId: ObjectId
  senderId: ObjectId
  receiverId: ObjectId
  content: string
  messageType: 'text' | 'file' | 'system'
  fileUrl?: string
  fileName?: string
  fileSize?: number
  isRead: boolean
  readAt?: Date
  isDeleted: boolean
  deletedAt?: Date
  createdAt: Date
  updatedAt: Date
}

// Conversation interface
export interface IConversation extends Document {
  _id: ObjectId
  participants: ObjectId[]
  applicationId?: ObjectId
  jobId?: ObjectId
  companyId?: ObjectId
  lastMessage?: {
    content: string
    senderId: ObjectId
    createdAt: Date
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Message schema
const MessageSchema = new Schema<IMessage>({
  conversationId: {
    type: Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
    index: true
  },
  senderId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  receiverId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  messageType: {
    type: String,
    enum: ['text', 'file', 'system'],
    default: 'text'
  },
  fileUrl: {
    type: String,
    validate: {
      validator: function(this: IMessage, v: string) {
        return this.messageType === 'file' ? !!v : true
      },
      message: 'File URL is required for file messages'
    }
  },
  fileName: {
    type: String,
    validate: {
      validator: function(this: IMessage, v: string) {
        return this.messageType === 'file' ? !!v : true
      },
      message: 'File name is required for file messages'
    }
  },
  fileSize: {
    type: Number,
    validate: {
      validator: function(this: IMessage, v: number) {
        return this.messageType === 'file' ? v > 0 : true
      },
      message: 'File size must be greater than 0 for file messages'
    }
  },
  isRead: {
    type: Boolean,
    default: false,
    index: true
  },
  readAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false,
    index: true
  },
  deletedAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Conversation schema
const ConversationSchema = new Schema<IConversation>({
  participants: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  applicationId: {
    type: Schema.Types.ObjectId,
    ref: 'Application',
    index: true
  },
  jobId: {
    type: Schema.Types.ObjectId,
    ref: 'Job',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  lastMessage: {
    content: String,
    senderId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: Date
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for better performance
MessageSchema.index({ conversationId: 1, createdAt: -1 })
MessageSchema.index({ senderId: 1, receiverId: 1 })
MessageSchema.index({ isRead: 1, receiverId: 1 })

ConversationSchema.index({ participants: 1 })
ConversationSchema.index({ applicationId: 1 })
ConversationSchema.index({ jobId: 1 })
ConversationSchema.index({ companyId: 1 })
ConversationSchema.index({ isActive: 1, updatedAt: -1 })

// Middleware to update conversation's lastMessage
MessageSchema.post('save', async function(doc) {
  try {
    await mongoose.model('Conversation').findByIdAndUpdate(
      doc.conversationId,
      {
        lastMessage: {
          content: doc.content,
          senderId: doc.senderId,
          createdAt: doc.createdAt
        },
        updatedAt: new Date()
      }
    )
  } catch (error) {
    console.error('Error updating conversation lastMessage:', error)
  }
})

// Middleware to validate participants array
ConversationSchema.pre('save', function(next) {
  if (this.participants.length < 2) {
    next(new Error('Conversation must have at least 2 participants'))
  } else {
    next()
  }
})

// Virtual for unread message count
ConversationSchema.virtual('unreadCount').get(function(this: IConversation) {
  // This would need to be populated separately in queries
  return 0
})

// Static methods
ConversationSchema.statics.findByParticipants = function(participantIds: ObjectId[]) {
  return this.findOne({
    participants: { $all: participantIds, $size: participantIds.length }
  })
}

ConversationSchema.statics.findUserConversations = function(userId: ObjectId) {
  return this.find({
    participants: userId,
    isActive: true
  }).sort({ updatedAt: -1 })
}

MessageSchema.statics.markAsRead = function(conversationId: ObjectId, userId: ObjectId) {
  return this.updateMany(
    {
      conversationId,
      receiverId: userId,
      isRead: false
    },
    {
      isRead: true,
      readAt: new Date()
    }
  )
}

// Create models
export const Message: Model<IMessage> = mongoose.models.Message || mongoose.model<IMessage>('Message', MessageSchema)
export const Conversation: Model<IConversation> = mongoose.models.Conversation || mongoose.model<IConversation>('Conversation', ConversationSchema)
