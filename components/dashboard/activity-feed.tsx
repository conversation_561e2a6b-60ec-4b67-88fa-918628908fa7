'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { 
  Users, 
  Calendar, 
  CheckCircle, 
  Briefcase, 
  AlertCircle,
  Clock,
  Eye,
  ExternalLink
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface ActivityItem {
  id: string
  type: 'application' | 'interview' | 'hire' | 'job_posted' | 'job_expired' | 'candidate_withdrawn'
  title: string
  description: string
  timestamp: Date
  status?: 'new' | 'scheduled' | 'completed' | 'cancelled'
  jobId?: string
  candidateId?: string
  applicationId?: string
}

interface ActivityFeedProps {
  activities: ActivityItem[]
  loading?: boolean
  onViewDetails?: (activity: ActivityItem) => void
  onRefresh?: () => void
  className?: string
  maxHeight?: string
}

export function ActivityFeed({
  activities,
  loading = false,
  onViewDetails,
  onRefresh,
  className,
  maxHeight = '400px'
}: ActivityFeedProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'application':
        return Users
      case 'interview':
        return Calendar
      case 'hire':
        return CheckCircle
      case 'job_posted':
        return Briefcase
      case 'job_expired':
        return Clock
      case 'candidate_withdrawn':
        return AlertCircle
      default:
        return AlertCircle
    }
  }

  const getActivityColor = (type: string, status?: string) => {
    if (status === 'cancelled') return 'text-red-600 bg-red-100'
    
    switch (type) {
      case 'application':
        return status === 'new' ? 'text-blue-600 bg-blue-100' : 'text-blue-600 bg-blue-50'
      case 'interview':
        return 'text-yellow-600 bg-yellow-100'
      case 'hire':
        return 'text-green-600 bg-green-100'
      case 'job_posted':
        return 'text-purple-600 bg-purple-100'
      case 'job_expired':
        return 'text-orange-600 bg-orange-100'
      case 'candidate_withdrawn':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusBadge = (status?: string) => {
    if (!status) return null

    const variants = {
      new: 'bg-blue-100 text-blue-800',
      scheduled: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    }

    return (
      <Badge className={cn('text-xs', variants[status as keyof typeof variants])}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest updates from your hiring process</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start space-x-4 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest updates from your hiring process</CardDescription>
        </div>
        {onRefresh && (
          <Button variant="outline" size="sm" onClick={onRefresh}>
            Refresh
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No recent activity</p>
            <p className="text-sm text-gray-400">Activity will appear here as candidates interact with your jobs</p>
          </div>
        ) : (
          <ScrollArea style={{ height: maxHeight }}>
            <div className="space-y-4">
              {activities.map((activity) => {
                const Icon = getActivityIcon(activity.type)
                const colorClasses = getActivityColor(activity.type, activity.status)
                
                return (
                  <div key={activity.id} className="flex items-start space-x-4 group">
                    <div className={cn(
                      'p-2 rounded-full flex-shrink-0',
                      colorClasses
                    )}>
                      <Icon className="w-4 h-4" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <p className="text-sm font-medium text-gray-900">
                              {activity.title}
                            </p>
                            {getStatusBadge(activity.status)}
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-1">
                            {activity.description}
                          </p>
                          
                          <p className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                          </p>
                        </div>
                        
                        {onViewDetails && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => onViewDetails(activity)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}

// Specialized activity feed for different contexts
export function DashboardActivityFeed({ 
  activities, 
  loading,
  onViewApplication,
  onViewJob
}: {
  activities: ActivityItem[]
  loading?: boolean
  onViewApplication?: (applicationId: string) => void
  onViewJob?: (jobId: string) => void
}) {
  const handleViewDetails = (activity: ActivityItem) => {
    if (activity.applicationId && onViewApplication) {
      onViewApplication(activity.applicationId)
    } else if (activity.jobId && onViewJob) {
      onViewJob(activity.jobId)
    }
  }

  return (
    <ActivityFeed
      activities={activities}
      loading={loading}
      onViewDetails={handleViewDetails}
      maxHeight="500px"
    />
  )
}

export function CompactActivityFeed({ 
  activities, 
  loading,
  limit = 5
}: {
  activities: ActivityItem[]
  loading?: boolean
  limit?: number
}) {
  const limitedActivities = activities.slice(0, limit)

  return (
    <div className="space-y-3">
      {loading ? (
        [...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-1">
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              <div className="h-2 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))
      ) : limitedActivities.length === 0 ? (
        <p className="text-sm text-gray-500 text-center py-4">No recent activity</p>
      ) : (
        limitedActivities.map((activity) => {
          const Icon = getActivityIcon(activity.type)
          const colorClasses = getActivityColor(activity.type, activity.status)
          
          return (
            <div key={activity.id} className="flex items-center space-x-3">
              <div className={cn('p-1.5 rounded-full', colorClasses)}>
                <Icon className="w-3 h-3" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {activity.title}
                </p>
                <p className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                </p>
              </div>
            </div>
          )
        })
      )}
    </div>
  )
}
