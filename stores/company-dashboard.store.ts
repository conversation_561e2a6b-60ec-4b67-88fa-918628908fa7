import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useAuthStore } from './auth.store'

// Types
export interface DashboardStats {
  activeJobs: number
  totalApplications: number
  pendingReviews: number
  interviewsScheduled: number
  hiredCandidates: number
  profileViews: number
  responseRate: number
  averageTimeToHire: number
  jobFillRate: number
  applicationQuality: number
  activeRecruiters: number
  // Growth metrics
  jobsGrowth: number
  applicationsGrowth: number
  profileViewsGrowth: number
  hiredGrowth: number
}

export interface RecentActivity {
  id: string
  type: 'application' | 'interview' | 'hire' | 'job_posted' | 'job_expired' | 'candidate_withdrawn'
  title: string
  description: string
  timestamp: Date
  status?: 'new' | 'scheduled' | 'completed' | 'cancelled'
  jobId?: string
  candidateId?: string
  applicationId?: string
}

export interface CompanyJob {
  _id: string
  title: string
  department: string
  location: string
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  status: 'active' | 'paused' | 'closed' | 'draft'
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  expiryDate?: Date
  salary?: {
    min: number
    max: number
    currency: string
  }
}

export interface CompanyApplication {
  _id: string
  jobId: string
  jobTitle: string
  candidateName: string
  candidateEmail: string
  status: 'pending' | 'reviewing' | 'shortlisted' | 'interviewed' | 'offered' | 'hired' | 'rejected'
  appliedAt: Date
  lastUpdated: Date
  rating?: number
  notes?: string
}

export interface HiringMetrics {
  totalHires: number
  averageTimeToHire: number
  costPerHire: number
  sourceEffectiveness: {
    source: string
    applications: number
    hires: number
    conversionRate: number
  }[]
  monthlyTrends: {
    month: string
    applications: number
    hires: number
    averageTime: number
  }[]
}

interface CompanyDashboardState {
  // Data
  stats: DashboardStats | null
  recentActivity: RecentActivity[]
  jobs: CompanyJob[]
  applications: CompanyApplication[]
  hiringMetrics: HiringMetrics | null
  currentJob: any | null
  currentApplication: any | null
  jobApplications: any[]

  // Loading states
  statsLoading: boolean
  activityLoading: boolean
  jobsLoading: boolean
  applicationsLoading: boolean
  metricsLoading: boolean
  jobLoading: boolean
  applicationLoading: boolean

  // Error states
  error: string | null
  
  // Pagination and filters
  jobsPage: number
  applicationsPage: number
  jobsFilter: {
    status?: string
    department?: string
    type?: string
  }
  applicationsFilter: {
    status?: string
    jobId?: string
    dateRange?: {
      start: Date
      end: Date
    }
  }
  
  // UI state
  selectedTimeRange: '7d' | '30d' | '90d' | '1y'
  lastUpdated: Date | null
}

interface CompanyDashboardActions {
  // Data fetching
  fetchDashboardStats: () => Promise<void>
  fetchRecentActivity: (limit?: number) => Promise<void>
  fetchCompanyJobs: (page?: number, filters?: any) => Promise<void>
  fetchApplications: (page?: number, filters?: any) => Promise<void>
  fetchHiringMetrics: (timeRange?: string) => Promise<void>
  fetchJobDetails: (jobId: string) => Promise<void>
  fetchJobApplications: (jobId: string) => Promise<void>
  fetchApplicationDetails: (applicationId: string) => Promise<void>

  // Job management
  createJob: (jobData: any) => Promise<void>
  updateJob: (jobId: string, updates: any) => Promise<void>
  deleteJob: (jobId: string) => Promise<void>
  toggleJobStatus: (jobId: string, status: string) => Promise<void>

  // Application management
  updateApplicationStatus: (applicationId: string, status: string, notes?: string) => Promise<void>
  bulkUpdateApplications: (applicationIds: string[], status: string) => Promise<void>
  scheduleInterview: (applicationId: string, interviewData: any) => Promise<void>
  addApplicationNote: (applicationId: string, note: string) => Promise<void>
  
  // Filters and pagination
  setJobsFilter: (filter: any) => void
  setApplicationsFilter: (filter: any) => void
  setTimeRange: (range: '7d' | '30d' | '90d' | '1y') => void
  
  // Utility
  clearError: () => void
  refreshDashboard: () => Promise<void>
  reset: () => void
}

type CompanyDashboardStore = CompanyDashboardState & CompanyDashboardActions

const initialState: CompanyDashboardState = {
  stats: null,
  recentActivity: [],
  jobs: [],
  applications: [],
  hiringMetrics: null,
  currentJob: null,
  currentApplication: null,
  jobApplications: [],
  statsLoading: false,
  activityLoading: false,
  jobsLoading: false,
  applicationsLoading: false,
  metricsLoading: false,
  jobLoading: false,
  applicationLoading: false,
  error: null,
  jobsPage: 1,
  applicationsPage: 1,
  jobsFilter: {},
  applicationsFilter: {},
  selectedTimeRange: '30d',
  lastUpdated: null
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = useAuthStore.getState().token
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  }
}

export const useCompanyDashboardStore = create<CompanyDashboardStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch dashboard stats
      fetchDashboardStats: async () => {
        set({ statsLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/companies/dashboard/stats', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            // If API doesn't exist, provide mock data for development
            if (response.status === 404) {
              set({
                stats: {
                  activeJobs: 5,
                  totalApplications: 23,
                  pendingReviews: 8,
                  interviewsScheduled: 3,
                  hiredCandidates: 2,
                  profileViews: 156,
                  responseRate: 85,
                  averageTimeToHire: 14,
                  jobFillRate: 75,
                  applicationQuality: 82,
                  activeRecruiters: 3,
                  jobsGrowth: 12,
                  applicationsGrowth: 18,
                  profileViewsGrowth: 25,
                  hiredGrowth: 8
                },
                statsLoading: false,
                lastUpdated: new Date()
              })
              return
            }
            throw new Error('Failed to fetch dashboard stats')
          }

          const data = await response.json()

          set({
            stats: data.data,
            statsLoading: false,
            lastUpdated: new Date()
          })
        } catch (error) {
          // Provide mock data for development
          set({
            stats: {
              activeJobs: 5,
              totalApplications: 23,
              pendingReviews: 8,
              interviewsScheduled: 3,
              hiredCandidates: 2,
              profileViews: 156,
              responseRate: 85,
              averageTimeToHire: 14,
              jobFillRate: 75,
              applicationQuality: 82,
              activeRecruiters: 3,
              jobsGrowth: 12,
              applicationsGrowth: 18,
              profileViewsGrowth: 25,
              hiredGrowth: 8
            },
            statsLoading: false,
            error: null
          })
        }
      },

      // Fetch recent activity
      fetchRecentActivity: async (limit = 10) => {
        set({ activityLoading: true, error: null })

        try {
          const response = await fetch(`/api/v1/companies/dashboard/activity?limit=${limit}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            // If API doesn't exist, provide mock data for development
            if (response.status === 404) {
              set({
                recentActivity: [
                  {
                    id: '1',
                    type: 'application',
                    title: 'New Application Received',
                    description: 'Sarah Johnson applied for Senior Frontend Developer',
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                    status: 'new'
                  },
                  {
                    id: '2',
                    type: 'interview',
                    title: 'Interview Scheduled',
                    description: 'Interview with John Doe for Backend Engineer position',
                    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
                    status: 'scheduled'
                  },
                  {
                    id: '3',
                    type: 'hire',
                    title: 'Candidate Hired',
                    description: 'Mike Chen accepted offer for DevOps Engineer',
                    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
                    status: 'completed'
                  }
                ],
                activityLoading: false
              })
              return
            }
            throw new Error('Failed to fetch recent activity')
          }

          const data = await response.json()

          set({
            recentActivity: data.data,
            activityLoading: false
          })
        } catch (error) {
          // Provide mock data for development
          set({
            recentActivity: [
              {
                id: '1',
                type: 'application',
                title: 'New Application Received',
                description: 'Sarah Johnson applied for Senior Frontend Developer',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                status: 'new'
              },
              {
                id: '2',
                type: 'interview',
                title: 'Interview Scheduled',
                description: 'Interview with John Doe for Backend Engineer position',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                status: 'scheduled'
              }
            ],
            activityLoading: false,
            error: null
          })
        }
      },

      // Fetch company jobs
      fetchCompanyJobs: async (page = 1, filters = {}) => {
        set({ jobsLoading: true, error: null })

        try {
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '10',
            ...filters
          })

          const response = await fetch(`/api/v1/companies/jobs?${params}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            // If API doesn't exist, set empty jobs array instead of error
            if (response.status === 404) {
              set({
                jobs: [],
                jobsPage: page,
                jobsLoading: false
              })
              return
            }
            throw new Error('Failed to fetch company jobs')
          }

          const data = await response.json()

          set({
            jobs: page === 1 ? data.data : [...get().jobs, ...data.data],
            jobsPage: page,
            jobsLoading: false
          })
        } catch (error) {
          // For development, set empty array instead of showing error
          set({
            jobs: [],
            jobsLoading: false,
            error: null // Don't show error for missing API endpoints
          })
        }
      },

      // Fetch applications
      fetchApplications: async (page = 1, filters = {}) => {
        set({ applicationsLoading: true, error: null })
        
        try {
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            ...filters
          })

          const response = await fetch(`/api/v1/companies/applications?${params}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch applications')
          }

          const data = await response.json()
          
          set({ 
            applications: page === 1 ? data.data : [...get().applications, ...data.data],
            applicationsPage: page,
            applicationsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications',
            applicationsLoading: false 
          })
        }
      },

      // Fetch hiring metrics
      fetchHiringMetrics: async (timeRange = '30d') => {
        set({ metricsLoading: true, error: null })
        
        try {
          const response = await fetch(`/api/v1/companies/dashboard/metrics?timeRange=${timeRange}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch hiring metrics')
          }

          const data = await response.json()
          
          set({ 
            hiringMetrics: data.data,
            metricsLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch metrics',
            metricsLoading: false 
          })
        }
      },

      // Create job
      createJob: async (jobData) => {
        try {
          const response = await fetch('/api/v1/jobs', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to create job')
          }

          // Refresh jobs list
          await get().fetchCompanyJobs()
          await get().fetchDashboardStats()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create job' })
          throw error
        }
      },

      // Update job
      updateJob: async (jobId, updates) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(updates)
          })

          if (!response.ok) {
            throw new Error('Failed to update job')
          }

          // Update local state
          set(state => ({
            jobs: state.jobs.map(job => 
              job._id === jobId ? { ...job, ...updates } : job
            )
          }))
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update job' })
          throw error
        }
      },

      // Delete job
      deleteJob: async (jobId) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to delete job')
          }

          // Remove from local state
          set(state => ({
            jobs: state.jobs.filter(job => job._id !== jobId)
          }))

          await get().fetchDashboardStats()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete job' })
          throw error
        }
      },

      // Toggle job status
      toggleJobStatus: async (jobId, status) => {
        await get().updateJob(jobId, { status })
      },

      // Update application status
      updateApplicationStatus: async (applicationId, status, notes) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/status`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify({ status, notes })
          })

          if (!response.ok) {
            throw new Error('Failed to update application status')
          }

          // Update local state
          set(state => ({
            applications: state.applications.map(app => 
              app._id === applicationId 
                ? { ...app, status: status as any, lastUpdated: new Date(), notes } 
                : app
            )
          }))

          await get().fetchDashboardStats()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update application' })
          throw error
        }
      },

      // Bulk update applications
      bulkUpdateApplications: async (applicationIds, status) => {
        try {
          const response = await fetch('/api/v1/applications/bulk-update', {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify({ applicationIds, status })
          })

          if (!response.ok) {
            throw new Error('Failed to bulk update applications')
          }

          // Update local state
          set(state => ({
            applications: state.applications.map(app => 
              applicationIds.includes(app._id) 
                ? { ...app, status: status as any, lastUpdated: new Date() } 
                : app
            )
          }))

          await get().fetchDashboardStats()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to bulk update applications' })
          throw error
        }
      },

      // Schedule interview
      scheduleInterview: async (applicationId, interviewData) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/interview`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(interviewData)
          })

          if (!response.ok) {
            throw new Error('Failed to schedule interview')
          }

          await get().updateApplicationStatus(applicationId, 'interviewed')
          await get().fetchRecentActivity()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to schedule interview' })
          throw error
        }
      },

      // Fetch job details
      fetchJobDetails: async (jobId: string) => {
        set({ jobLoading: true, error: null })

        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch job details')
          }

          const data = await response.json()

          set({
            currentJob: data.data,
            jobLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch job details',
            jobLoading: false
          })
        }
      },

      // Fetch job applications
      fetchJobApplications: async (jobId: string) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}/applications`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch job applications')
          }

          const data = await response.json()

          set({
            jobApplications: data.data || []
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch job applications'
          })
        }
      },

      // Fetch application details
      fetchApplicationDetails: async (applicationId: string) => {
        set({ applicationLoading: true, error: null })

        try {
          const response = await fetch(`/api/v1/applications/${applicationId}`, {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch application details')
          }

          const data = await response.json()

          set({
            currentApplication: data.data,
            applicationLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch application details',
            applicationLoading: false
          })
        }
      },

      // Add application note
      addApplicationNote: async (applicationId: string, note: string) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/notes`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ note })
          })

          if (!response.ok) {
            throw new Error('Failed to add note')
          }

          // Refresh application details
          await get().fetchApplicationDetails(applicationId)
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to add note' })
          throw error
        }
      },

      // Set filters
      setJobsFilter: (filter) => {
        set({ jobsFilter: { ...get().jobsFilter, ...filter } })
      },

      setApplicationsFilter: (filter) => {
        set({ applicationsFilter: { ...get().applicationsFilter, ...filter } })
      },

      setTimeRange: (range) => {
        set({ selectedTimeRange: range })
        get().fetchHiringMetrics(range)
      },

      // Utility functions
      clearError: () => set({ error: null }),

      refreshDashboard: async () => {
        await Promise.all([
          get().fetchDashboardStats(),
          get().fetchRecentActivity(),
          get().fetchCompanyJobs(),
          get().fetchApplications()
        ])
      },

      reset: () => set(initialState)
    }),
    {
      name: 'company-dashboard-storage',
      partialize: (state) => ({
        selectedTimeRange: state.selectedTimeRange,
        jobsFilter: state.jobsFilter,
        applicationsFilter: state.applicationsFilter
      })
    }
  )
)
