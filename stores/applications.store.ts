import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface Application {
  _id: string
  jobId: string
  userId: string
  job: {
    _id: string
    title: string
    company: {
      name: string
      logo?: string
    }
    location: {
      city: string
      state: string
      remote: boolean
    }
    type: string
    salary?: {
      min?: number
      max?: number
      currency: string
      period: string
    }
  }
  status: 'submitted' | 'under_review' | 'interview_scheduled' | 'interviewed' | 'offer_extended' | 'rejected' | 'withdrawn'
  coverLetter: string
  resumeId: string
  portfolioUrl?: string
  linkedinUrl?: string
  availableStartDate: Date
  salaryExpectation?: string
  willingToRelocate: boolean
  customFields: Record<string, unknown>
  timeline: ApplicationTimelineEvent[]
  submittedAt: Date
  updatedAt: Date
}

export interface ApplicationTimelineEvent {
  _id: string
  type: 'submitted' | 'viewed' | 'status_changed' | 'interview_scheduled' | 'note_added'
  status?: Application['status']
  message: string
  createdAt: Date
  createdBy?: {
    name: string
    role: string
  }
}

export interface ApplicationFilters {
  status: Application['status'][]
  dateRange: {
    from?: Date
    to?: Date
  }
  jobTypes: string[]
  companies: string[]
}

export interface ApplicationStats {
  total: number
  submitted: number
  underReview: number
  interviewed: number
  offers: number
  rejected: number
  responseRate: number
  averageResponseTime: number
}

interface ApplicationsState {
  applications: Application[]
  currentApplication: Application | null
  filters: Partial<ApplicationFilters>
  stats: ApplicationStats | null

  // Loading states
  applicationsLoading: boolean
  applicationLoading: boolean
  statsLoading: boolean
  withdrawLoading: boolean

  // Error states
  error: AppError | null
  applicationsError: AppError | null

  // Meta
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface ApplicationsActions {
  getApplications: (filters?: Partial<ApplicationFilters>) => Promise<void>
  getApplicationById: (applicationId: string) => Promise<void>
  withdrawApplication: (applicationId: string, reason?: string) => Promise<void>
  getApplicationStats: () => Promise<void>
  updateFilters: (filters: Partial<ApplicationFilters>) => void
  clearFilters: () => void
  clearError: () => void
  clearApplicationsError: () => void
}

// API Service functions
const ApplicationsAPI = {
  async getApplications(filters?: Partial<ApplicationFilters>, page = 1, limit = 20) {
    const params = new URLSearchParams()
    params.append('page', page.toString())
    params.append('limit', limit.toString())
    
    if (filters?.status?.length) {
      filters.status.forEach(status => params.append('status', status))
    }
    if (filters?.jobTypes?.length) {
      filters.jobTypes.forEach(type => params.append('jobType', type))
    }
    if (filters?.companies?.length) {
      filters.companies.forEach(company => params.append('company', company))
    }
    if (filters?.dateRange?.from) {
      params.append('dateFrom', filters.dateRange.from.toISOString())
    }
    if (filters?.dateRange?.to) {
      params.append('dateTo', filters.dateRange.to.toISOString())
    }

    const response = await fetch(`/api/applications?${params}`, {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch applications')
    }
    return response.json()
  },

  async getApplicationById(applicationId: string) {
    const response = await fetch(`/api/applications/${applicationId}`, {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch application')
    }
    return response.json()
  },

  async withdrawApplication(applicationId: string, reason?: string) {
    const response = await fetch(`/api/applications/${applicationId}/withdraw`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
      },
      body: JSON.stringify({ reason })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to withdraw application')
    }
    return response.json()
  },

  async getApplicationStats() {
    const response = await fetch('/api/applications/stats', {
      headers: { 'Authorization': `Bearer ${localStorage.getItem('auth-token')}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch application stats')
    }
    return response.json()
  }
}

export const useApplicationsStore = create<ApplicationsState & ApplicationsActions>()(
  persist(
    (set, get) => ({
      // State
      applications: [],
      currentApplication: null,
      filters: {},
      stats: null,

      // Loading states
      applicationsLoading: false,
      applicationLoading: false,
      statsLoading: false,
      withdrawLoading: false,

      // Error states
      error: null,
      applicationsError: null,

      // Meta
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },

      // Actions
      getApplications: async (filters) => {
        set({ applicationsLoading: true, applicationsError: null })
        
        try {
          const response = await ApplicationsAPI.getApplications(
            filters || get().filters,
            get().pagination.page,
            get().pagination.limit
          )
          
          set({
            applications: response.applications,
            pagination: response.pagination,
            applicationsLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'applications_get')
          set({ 
            applicationsError: appError, 
            applicationsLoading: false 
          })
          throw appError
        }
      },

      getApplicationById: async (applicationId) => {
        set({ applicationLoading: true, error: null })
        
        try {
          const application = await ApplicationsAPI.getApplicationById(applicationId)
          set({
            currentApplication: application,
            applicationLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'applications_get_by_id')
          set({
            error: appError,
            applicationLoading: false
          })
          throw appError
        }
      },

      withdrawApplication: async (applicationId, reason) => {
        set({ withdrawLoading: true, error: null })
        
        try {
          await ApplicationsAPI.withdrawApplication(applicationId, reason)
          
          // Update application status in store
          set(state => ({
            applications: state.applications.map(app =>
              app._id === applicationId 
                ? { ...app, status: 'withdrawn' as const }
                : app
            ),
            currentApplication: state.currentApplication?._id === applicationId
              ? { ...state.currentApplication, status: 'withdrawn' as const }
              : state.currentApplication,
            withdrawLoading: false
          }))
        } catch (error) {
          const appError = errorService.logError(error as Error, 'applications_withdraw')
          set({
            error: appError,
            withdrawLoading: false
          })
          throw appError
        }
      },

      getApplicationStats: async () => {
        set({ statsLoading: true, error: null })
        
        try {
          const stats = await ApplicationsAPI.getApplicationStats()
          set({
            stats,
            statsLoading: false
          })
        } catch (error) {
          const appError = errorService.logError(error as Error, 'applications_stats')
          set({
            error: appError,
            statsLoading: false
          })
        }
      },

      updateFilters: (filters) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      clearFilters: () => {
        set({
          filters: {},
          pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
        })
      },

      clearError: () => set({ error: null }),
      clearApplicationsError: () => set({ applicationsError: null })
    }),
    {
      name: 'applications-storage',
      partialize: (state) => ({
        // Only persist filters, not the actual data
        filters: state.filters
      })
    }
  )
)
