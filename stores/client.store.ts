import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import {
  Client,
  ClientProfile,
  UpdateClientRequest,
  ClientSearchFilters,
  ClientSearchResult
} from '@/types/client.types'
import { useAuthStore } from './auth.store'

interface ClientState {
  // Current client data
  client: ClientProfile | null
  
  // Loading states
  loading: boolean
  profileLoading: boolean
  updateLoading: boolean
  searchLoading: boolean
  
  // Error state
  error: string | null
  
  // Search results
  searchResults: ClientSearchResult | null
  searchFilters: ClientSearchFilters
  
  // Saved jobs and followed companies
  savedJobs: string[]
  followedCompanies: string[]
  
  // Application stats
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
  }
}

interface ClientActions {
  // Profile management
  fetchClientProfile: () => Promise<void>
  updateClientProfile: (data: UpdateClientRequest) => Promise<void>
  
  // Job management
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  
  // Company management
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  
  // Search
  searchClients: (filters: ClientSearchFilters, page?: number, limit?: number) => Promise<void>
  updateSearchFilters: (filters: Partial<ClientSearchFilters>) => void
  clearSearchResults: () => void
  
  // Activity tracking
  updateProfileViews: () => Promise<void>
  
  // Utility
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

type ClientStore = ClientState & ClientActions

const initialState: ClientState = {
  client: null,
  loading: false,
  profileLoading: false,
  updateLoading: false,
  searchLoading: false,
  error: null,
  searchResults: null,
  searchFilters: {},
  savedJobs: [],
  followedCompanies: [],
  applicationStats: {
    totalApplications: 0,
    pendingApplications: 0,
    interviewsReceived: 0,
    offersReceived: 0,
    successRate: 0
  }
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = useAuthStore.getState().token
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  }
}

export const useClientStore = create<ClientStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Profile management
      fetchClientProfile: async () => {
        set({ profileLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/clients/me', {
            headers: getAuthHeaders()
          })

          if (!response.ok) {
            throw new Error('Failed to fetch client profile')
          }

          const data = await response.json()

          set({
            client: data.data,
            savedJobs: data.data.savedJobs || [],
            followedCompanies: data.data.followedCompanies || [],
            applicationStats: data.data.applicationStats || initialState.applicationStats,
            profileLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch profile',
            profileLoading: false
          })
        }
      },

      updateClientProfile: async (data: UpdateClientRequest) => {
        set({ updateLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/clients/me', {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to update client profile')
          }

          const result = await response.json()
          
          set({ 
            client: result.data,
            updateLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update profile',
            updateLoading: false 
          })
        }
      },

      // Job management
      saveJob: async (jobId: string) => {
        const { client, savedJobs } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/save-job`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ jobId })
          })

          if (!response.ok) {
            throw new Error('Failed to save job')
          }

          set({ 
            savedJobs: [...savedJobs, jobId]
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to save job'
          })
        }
      },

      unsaveJob: async (jobId: string) => {
        const { client, savedJobs } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/unsave-job`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ jobId })
          })

          if (!response.ok) {
            throw new Error('Failed to unsave job')
          }

          set({ 
            savedJobs: savedJobs.filter(id => id !== jobId)
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to unsave job'
          })
        }
      },

      // Company management
      followCompany: async (companyId: string) => {
        const { client, followedCompanies } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/follow-company`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ companyId })
          })

          if (!response.ok) {
            throw new Error('Failed to follow company')
          }

          set({ 
            followedCompanies: [...followedCompanies, companyId]
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to follow company'
          })
        }
      },

      unfollowCompany: async (companyId: string) => {
        const { client, followedCompanies } = get()
        if (!client) return

        try {
          const response = await fetch(`/api/v1/clients/${client._id}/unfollow-company`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ companyId })
          })

          if (!response.ok) {
            throw new Error('Failed to unfollow company')
          }

          set({ 
            followedCompanies: followedCompanies.filter(id => id !== companyId)
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to unfollow company'
          })
        }
      },

      // Search
      searchClients: async (filters: ClientSearchFilters, page = 1, limit = 20) => {
        set({ searchLoading: true, error: null })
        
        try {
          const searchParams = new URLSearchParams()
          
          if (filters.skills?.length) searchParams.set('skills', filters.skills.join(','))
          if (filters.experience?.length) searchParams.set('experience', filters.experience.join(','))
          if (filters.industries?.length) searchParams.set('industries', filters.industries.join(','))
          if (filters.locations?.length) searchParams.set('locations', filters.locations.join(','))
          if (filters.availability?.length) searchParams.set('availability', filters.availability.join(','))
          if (filters.salaryMin) searchParams.set('salaryMin', filters.salaryMin.toString())
          if (filters.salaryMax) searchParams.set('salaryMax', filters.salaryMax.toString())
          if (filters.profileVisibility?.length) searchParams.set('profileVisibility', filters.profileVisibility.join(','))
          
          searchParams.set('page', page.toString())
          searchParams.set('limit', limit.toString())

          const response = await fetch(`/api/v1/clients?${searchParams}`)

          if (!response.ok) {
            throw new Error('Failed to search clients')
          }

          const data = await response.json()
          
          set({ 
            searchResults: data.data,
            searchFilters: filters,
            searchLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to search clients',
            searchLoading: false 
          })
        }
      },

      updateSearchFilters: (filters: Partial<ClientSearchFilters>) => {
        set(state => ({
          searchFilters: { ...state.searchFilters, ...filters }
        }))
      },

      clearSearchResults: () => {
        set({ 
          searchResults: null,
          searchFilters: {}
        })
      },

      // Activity tracking
      updateProfileViews: async () => {
        const { client } = get()
        if (!client) return

        try {
          await fetch(`/api/v1/clients/${client._id}/activity`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ activityType: 'view' })
          })
        } catch (error) {
          // Silently fail for activity tracking
          console.error('Failed to update profile views:', error)
        }
      },

      // Utility
      setLoading: (loading: boolean) => set({ loading }),
      
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState)
    }),
    {
      name: 'client-store',
      partialize: (state) => ({
        client: state.client,
        savedJobs: state.savedJobs,
        followedCompanies: state.followedCompanies,
        searchFilters: state.searchFilters
      })
    }
  )
)
