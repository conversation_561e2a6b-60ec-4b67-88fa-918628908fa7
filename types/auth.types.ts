// Authentication and authorization types

export interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  companyId?: string
  adminRole?: AdminRole
  permissions?: string[]
  iat?: number
  exp?: number
  iss?: string
  sub?: string
}

export type UserRole = 
  | 'job_seeker' 
  | 'recruiter' 
  | 'company_admin' 
  | 'admin'

export type AdminRole = 
  | 'super_admin' 
  | 'content_moderator' 
  | 'user_manager' 
  | 'analytics_viewer'

export interface AuthenticatedUser {
  id: string
  email: string
  role: UserRole
  companyId?: string
  adminRole?: AdminRole
  permissions?: string[]
  isActive: boolean
  emailVerified: boolean
  profile?: {
    firstName?: string
    lastName?: string
    avatar?: string
  }
}

export interface AuthenticationResult {
  user: AuthenticatedUser | null
  error?: string
}

export interface AuthMiddlewareResult {
  success: boolean
  user?: AuthenticatedUser
  error?: string
  status?: number
}

export interface AuthorizationResult {
  authorized: boolean
  error?: string
  requiredRole?: string
  userRole?: string
}

export interface TokenGenerationOptions {
  expiresIn?: string | number
  issuer?: string
  audience?: string | string[]
  subject?: string
}

export interface AuthContext {
  user: AuthenticatedUser
  permissions: string[]
  isAuthenticated: boolean
  isAdmin: boolean
  isCompanyAdmin: boolean
  isRecruiter: boolean
  isJobSeeker: boolean
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
  companyId?: string
  acceptTerms: boolean
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

export interface EmailVerificationRequest {
  token: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

// Permission constants
export const PERMISSIONS = {
  // User management
  USER_READ: 'user:read',
  USER_WRITE: 'user:write',
  USER_DELETE: 'user:delete',
  
  // Company management
  COMPANY_READ: 'company:read',
  COMPANY_WRITE: 'company:write',
  COMPANY_DELETE: 'company:delete',
  COMPANY_VERIFY: 'company:verify',
  
  // Job management
  JOB_READ: 'job:read',
  JOB_WRITE: 'job:write',
  JOB_DELETE: 'job:delete',
  JOB_MODERATE: 'job:moderate',
  
  // Application management
  APPLICATION_READ: 'application:read',
  APPLICATION_WRITE: 'application:write',
  APPLICATION_DELETE: 'application:delete',
  
  // Admin functions
  ADMIN_DASHBOARD: 'admin:dashboard',
  ADMIN_ANALYTICS: 'admin:analytics',
  ADMIN_SETTINGS: 'admin:settings',
  ADMIN_LOGS: 'admin:logs',
  
  // Content moderation
  CONTENT_MODERATE: 'content:moderate',
  CONTENT_REVIEW: 'content:review',
  
  // System functions
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_MAINTENANCE: 'system:maintenance'
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  job_seeker: [
    PERMISSIONS.JOB_READ,
    PERMISSIONS.APPLICATION_READ,
    PERMISSIONS.APPLICATION_WRITE
  ],
  recruiter: [
    PERMISSIONS.JOB_READ,
    PERMISSIONS.JOB_WRITE,
    PERMISSIONS.APPLICATION_READ,
    PERMISSIONS.COMPANY_READ
  ],
  company_admin: [
    PERMISSIONS.JOB_READ,
    PERMISSIONS.JOB_WRITE,
    PERMISSIONS.JOB_DELETE,
    PERMISSIONS.APPLICATION_READ,
    PERMISSIONS.APPLICATION_WRITE,
    PERMISSIONS.COMPANY_READ,
    PERMISSIONS.COMPANY_WRITE,
    PERMISSIONS.USER_READ
  ],
  admin: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.COMPANY_READ,
    PERMISSIONS.COMPANY_WRITE,
    PERMISSIONS.COMPANY_DELETE,
    PERMISSIONS.COMPANY_VERIFY,
    PERMISSIONS.JOB_READ,
    PERMISSIONS.JOB_WRITE,
    PERMISSIONS.JOB_DELETE,
    PERMISSIONS.JOB_MODERATE,
    PERMISSIONS.APPLICATION_READ,
    PERMISSIONS.APPLICATION_WRITE,
    PERMISSIONS.APPLICATION_DELETE,
    PERMISSIONS.ADMIN_DASHBOARD,
    PERMISSIONS.ADMIN_ANALYTICS,
    PERMISSIONS.ADMIN_SETTINGS,
    PERMISSIONS.ADMIN_LOGS,
    PERMISSIONS.CONTENT_MODERATE,
    PERMISSIONS.CONTENT_REVIEW,
    PERMISSIONS.SYSTEM_BACKUP,
    PERMISSIONS.SYSTEM_MAINTENANCE
  ]
}

// Admin role specific permissions
export const ADMIN_ROLE_PERMISSIONS: Record<AdminRole, Permission[]> = {
  super_admin: Object.values(PERMISSIONS),
  content_moderator: [
    PERMISSIONS.CONTENT_MODERATE,
    PERMISSIONS.CONTENT_REVIEW,
    PERMISSIONS.JOB_MODERATE,
    PERMISSIONS.ADMIN_DASHBOARD
  ],
  user_manager: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.COMPANY_READ,
    PERMISSIONS.COMPANY_WRITE,
    PERMISSIONS.ADMIN_DASHBOARD
  ],
  analytics_viewer: [
    PERMISSIONS.ADMIN_ANALYTICS,
    PERMISSIONS.ADMIN_DASHBOARD
  ]
}
