#!/usr/bin/env tsx

/**
 * API Routes Migration Script
 * 
 * This script helps migrate API routes from app/api/ to app/api/v1/
 * and updates frontend code to use the new endpoints.
 */

import fs from 'fs'
import path from 'path'
import { glob } from 'glob'

interface RouteMapping {
  oldPath: string
  newPath: string
  status: 'pending' | 'migrated' | 'deprecated'
}

const ROUTE_MAPPINGS: RouteMapping[] = [
  // Authentication routes
  { oldPath: '/api/auth/login', newPath: '/api/v1/auth/login', status: 'migrated' },
  { oldPath: '/api/auth/register', newPath: '/api/v1/auth/register', status: 'migrated' },
  { oldPath: '/api/auth/me', newPath: '/api/v1/auth/me', status: 'migrated' },
  { oldPath: '/api/auth/refresh', newPath: '/api/v1/auth/refresh', status: 'migrated' },
  { oldPath: '/api/auth/logout', newPath: '/api/v1/auth/logout', status: 'migrated' },
  
  // Companies routes
  { oldPath: '/api/companies', newPath: '/api/v1/companies', status: 'migrated' },
  { oldPath: '/api/companies/', newPath: '/api/v1/companies/', status: 'migrated' },
  
  // Jobs routes
  { oldPath: '/api/jobs', newPath: '/api/v1/jobs', status: 'migrated' },
  { oldPath: '/api/jobs/', newPath: '/api/v1/jobs/', status: 'migrated' },
  
  // Applications routes
  { oldPath: '/api/applications', newPath: '/api/v1/applications', status: 'migrated' },
  { oldPath: '/api/applications/', newPath: '/api/v1/applications/', status: 'migrated' },
  
  // Admin routes (need migration)
  { oldPath: '/api/admin/', newPath: '/api/v1/admin/', status: 'pending' },
  
  // Messages routes (need migration)
  { oldPath: '/api/messages/', newPath: '/api/v1/messages/', status: 'pending' }
]

async function findFilesToUpdate(): Promise<string[]> {
  const patterns = [
    'app/**/*.ts',
    'app/**/*.tsx',
    'components/**/*.ts',
    'components/**/*.tsx',
    'stores/**/*.ts',
    'lib/**/*.ts',
    'hooks/**/*.ts'
  ]
  
  const files: string[] = []
  
  for (const pattern of patterns) {
    const matches = await glob(pattern, { ignore: ['node_modules/**', '.next/**'] })
    files.push(...matches)
  }
  
  return files
}

async function updateFileContent(filePath: string): Promise<boolean> {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let updatedContent = content
    let hasChanges = false
    
    for (const mapping of ROUTE_MAPPINGS) {
      if (mapping.status === 'migrated') {
        const regex = new RegExp(mapping.oldPath.replace(/\//g, '\\/'), 'g')
        if (regex.test(updatedContent)) {
          updatedContent = updatedContent.replace(regex, mapping.newPath)
          hasChanges = true
          console.log(`  ✓ Updated ${mapping.oldPath} → ${mapping.newPath}`)
        }
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    console.error(`Error updating file ${filePath}:`, error)
    return false
  }
}

async function checkForOldApiCalls(filePath: string): Promise<string[]> {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const oldApiCalls: string[] = []
    
    // Check for old API patterns
    const apiCallPatterns = [
      /['"`]\/api\/(?!v1\/)[^'"`]+['"`]/g,
      /fetch\s*\(\s*['"`]\/api\/(?!v1\/)[^'"`]+['"`]/g,
      /axios\.[get|post|put|delete]+\s*\(\s*['"`]\/api\/(?!v1\/)[^'"`]+['"`]/g
    ]
    
    for (const pattern of apiCallPatterns) {
      const matches = content.match(pattern)
      if (matches) {
        oldApiCalls.push(...matches)
      }
    }
    
    return [...new Set(oldApiCalls)] // Remove duplicates
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error)
    return []
  }
}

async function generateMigrationReport(): Promise<void> {
  console.log('🔍 Scanning for API route usage...\n')
  
  const files = await findFilesToUpdate()
  const report = {
    totalFiles: files.length,
    filesWithOldApiCalls: 0,
    oldApiCallsFound: [] as Array<{ file: string; calls: string[] }>
  }
  
  for (const file of files) {
    const oldCalls = await checkForOldApiCalls(file)
    if (oldCalls.length > 0) {
      report.filesWithOldApiCalls++
      report.oldApiCallsFound.push({ file, calls: oldCalls })
    }
  }
  
  console.log('📊 Migration Report:')
  console.log(`Total files scanned: ${report.totalFiles}`)
  console.log(`Files with old API calls: ${report.filesWithOldApiCalls}`)
  console.log('')
  
  if (report.oldApiCallsFound.length > 0) {
    console.log('🚨 Files that need updating:')
    for (const item of report.oldApiCallsFound) {
      console.log(`\n📄 ${item.file}:`)
      for (const call of item.calls) {
        console.log(`  - ${call}`)
      }
    }
  } else {
    console.log('✅ No old API calls found!')
  }
}

async function updateFrontendFiles(): Promise<void> {
  console.log('🔄 Updating frontend files...\n')
  
  const files = await findFilesToUpdate()
  let updatedFiles = 0
  
  for (const file of files) {
    console.log(`Checking ${file}...`)
    const wasUpdated = await updateFileContent(file)
    if (wasUpdated) {
      updatedFiles++
      console.log(`  ✅ Updated ${file}`)
    }
  }
  
  console.log(`\n📈 Summary: Updated ${updatedFiles} files`)
}

async function main(): Promise<void> {
  const command = process.argv[2]
  
  switch (command) {
    case 'report':
      await generateMigrationReport()
      break
    case 'update':
      await updateFrontendFiles()
      break
    case 'check':
      await generateMigrationReport()
      console.log('\n' + '='.repeat(50))
      console.log('Run "npm run migrate-api update" to apply changes')
      break
    default:
      console.log('API Routes Migration Tool')
      console.log('')
      console.log('Usage:')
      console.log('  npm run migrate-api report  - Generate migration report')
      console.log('  npm run migrate-api update  - Update frontend files')
      console.log('  npm run migrate-api check   - Check and show what needs updating')
      break
  }
}

if (require.main === module) {
  main().catch(console.error)
}

export { ROUTE_MAPPINGS, findFilesToUpdate, updateFileContent, checkForOldApiCalls }
