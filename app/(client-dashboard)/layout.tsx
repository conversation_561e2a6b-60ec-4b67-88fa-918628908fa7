// app/(client-dashboard)/layout.tsx
"use client"

import React from "react"
import { ClientDashboardTopbar } from "@/components/client/client-dashboard-topbar"
import { ClientDashboardSidebar } from "@/components/client/client-dashboard-sidebar"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"
import { SidebarProvider } from "@/components/ui/sidebar"

interface ClientDashboardLayoutProps {
  children: React.ReactNode
}

export default function ClientDashboardLayout({
  children
}: ClientDashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-background">
        <BackgroundPattern />
        
        {/* Redesigned Full-Width Client Dashboard Layout */}
        <div className="flex h-screen w-screen overflow-hidden">
          {/* Sidebar - Fixed Width */}
          <ClientDashboardSidebar />

          {/* Main Content Area - Takes Remaining Space */}
          <div className="flex-1 flex flex-col min-w-0 w-full">
            {/* Top Navigation - Full Width */}
            <ClientDashboardTopbar />

            {/* Content Area - Full Width, No Constraints */}
            <main className="flex-1 w-full overflow-auto">
              {children}
            </main>
          </div>
        </div>
        
        <NotificationSystem />
      </div>
    </SidebarProvider>
  )
}
