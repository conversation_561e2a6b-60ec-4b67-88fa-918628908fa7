import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'
import { ApiResponse, AuthUser, DatabaseError } from '@/lib/types/api.types'

export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<{ user: AuthUser }>>> {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: authResult.error || 'Authentication required',
            statusCode: authResult.status || 401
          },
          timestamp: new Date().toISOString()
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user.id

    // Get full user data from database
    const user = await User.findById(userId)
      .populate('companyId', 'name slug')
      .select('-password -refreshTokens')
      .lean()

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            statusCode: 404
          },
          timestamp: new Date().toISOString()
        },
        { status: 404 }
      )
    }

    // Format user data
    const userData: AuthUser = {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      profile: user.profile || {
        firstName: '',
        lastName: '',
        fullName: ''
      },
      preferences: user.preferences || {
        emailNotifications: true,
        jobAlerts: true,
        marketingEmails: false,
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        remoteWork: false
      },
      companyId: user.companyId?._id?.toString(),
      isEmailVerified: user.isEmailVerified || false,
      isActive: user.isActive || true,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }

    return NextResponse.json(
      {
        success: true,
        data: { user: userData },
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Get user profile error:', error)
    
    const dbError = error as DatabaseError
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch user profile',
          statusCode: 500,
          details: process.env.NODE_ENV === 'development' ? { originalError: dbError.message } : undefined
        },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function POST(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'POST method not allowed. Use PUT to update user profile.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'DELETE method not allowed. Use DELETE /api/v1/users/[id] to delete user account.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}
