import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'
import { ApiResponse, DatabaseError } from '@/lib/types/api.types'

interface LogoutRequest {
  refreshToken?: string
  logoutAll?: boolean
}

export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: authResult.error || 'Authentication required',
            statusCode: authResult.status || 401
          },
          timestamp: new Date().toISOString()
        },
        { status: authResult.status || 401 }
      )
    }

    const userId = authResult.user.id

    let body: LogoutRequest = {}
    try {
      body = await request.json()
    } catch {
      // Body is optional for logout
    }

    const { refreshToken, logoutAll = false } = body

    // Get user from database
    const user = await User.findById(userId)
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            statusCode: 404
          },
          timestamp: new Date().toISOString()
        },
        { status: 404 }
      )
    }

    if (logoutAll) {
      // Clear all refresh tokens (logout from all devices)
      user.refreshTokens = []
    } else if (refreshToken) {
      // Remove specific refresh token
      user.refreshTokens = user.refreshTokens?.filter(token => token !== refreshToken) || []
    } else {
      // If no refresh token provided, we can't remove it from the database
      // but we can still return success as the client will discard the tokens
    }

    await user.save()

    const message = logoutAll 
      ? 'Logged out from all devices successfully'
      : 'Logged out successfully'

    return NextResponse.json(
      {
        success: true,
        data: { message },
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Logout error:', error)
    
    const dbError = error as DatabaseError
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to logout',
          statusCode: 500,
          details: process.env.NODE_ENV === 'development' ? { originalError: dbError.message } : undefined
        },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function GET(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'GET method not allowed. Use POST to logout.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'PUT method not allowed. Use POST to logout.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'DELETE method not allowed. Use POST to logout.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}
