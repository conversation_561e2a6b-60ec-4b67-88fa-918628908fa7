import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'

interface RouteParams {
  params: {
    id: string
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const jobId = params.id
    const { status, reason } = await request.json()

    // Validate status
    const validStatuses = ['draft', 'active', 'paused', 'closed', 'expired']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      )
    }

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the job and verify ownership
    const job = await Job.findById(jobId)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    if (job.company.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Job does not belong to your company' },
        { status: 403 }
      )
    }

    // Validate status transitions
    const currentStatus = job.status
    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'closed'],
      'active': ['paused', 'closed', 'expired'],
      'paused': ['active', 'closed'],
      'closed': ['active'], // Allow reopening
      'expired': ['active', 'closed']
    }

    if (!validTransitions[currentStatus]?.includes(status)) {
      return NextResponse.json(
        { 
          error: `Invalid status transition from ${currentStatus} to ${status}`,
          validTransitions: validTransitions[currentStatus] || []
        },
        { status: 400 }
      )
    }

    // Update job status
    const updateData: any = {
      status,
      updatedAt: new Date(),
      updatedBy: userId
    }

    // Set specific fields based on status
    switch (status) {
      case 'active':
        updateData.isActive = true
        updateData.publishedAt = job.publishedAt || new Date()
        break
      case 'paused':
        updateData.isActive = false
        break
      case 'closed':
        updateData.isActive = false
        updateData.closedAt = new Date()
        updateData.closedBy = userId
        if (reason) {
          updateData.closedReason = reason
        }
        break
      case 'expired':
        updateData.isActive = false
        updateData.expiredAt = new Date()
        break
      case 'draft':
        updateData.isActive = false
        break
    }

    // Add status history entry
    if (!job.statusHistory) {
      job.statusHistory = []
    }
    
    job.statusHistory.push({
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason: reason || undefined,
      timestamp: new Date()
    })

    updateData.statusHistory = job.statusHistory

    const updatedJob = await Job.findByIdAndUpdate(
      jobId,
      updateData,
      { new: true, runValidators: true }
    ).populate('company', 'name logo')

    // Log the status change
    console.log(`Job ${jobId} status changed from ${currentStatus} to ${status} by user ${userId}`)

    return NextResponse.json({
      success: true,
      message: `Job status updated to ${status}`,
      data: {
        id: updatedJob._id,
        title: updatedJob.title,
        status: updatedJob.status,
        isActive: updatedJob.isActive,
        updatedAt: updatedJob.updatedAt,
        statusHistory: updatedJob.statusHistory
      }
    })

  } catch (error) {
    console.error('Update job status error:', error)
    return NextResponse.json(
      { error: 'Failed to update job status' },
      { status: 500 }
    )
  }
}

// GET method to retrieve current status and valid transitions
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const jobId = params.id

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the job and verify ownership
    const job = await Job.findById(jobId).select('status isActive statusHistory company')
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    if (job.company.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Job does not belong to your company' },
        { status: 403 }
      )
    }

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'closed'],
      'active': ['paused', 'closed', 'expired'],
      'paused': ['active', 'closed'],
      'closed': ['active'],
      'expired': ['active', 'closed']
    }

    return NextResponse.json({
      success: true,
      data: {
        currentStatus: job.status,
        isActive: job.isActive,
        validTransitions: validTransitions[job.status] || [],
        statusHistory: job.statusHistory || []
      }
    })

  } catch (error) {
    console.error('Get job status error:', error)
    return NextResponse.json(
      { error: 'Failed to get job status' },
      { status: 500 }
    )
  }
}
