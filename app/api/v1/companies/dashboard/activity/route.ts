import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Get recent activities from multiple sources
    const activities = []

    // Recent applications
    const recentApplications = await Application.find({
      company: companyId
    })
    .populate('job', 'title')
    .populate('client', 'user')
    .populate({
      path: 'client',
      populate: {
        path: 'user',
        select: 'profile.firstName profile.lastName'
      }
    })
    .sort({ createdAt: -1 })
    .limit(limit)

    // Add application activities
    recentApplications.forEach(app => {
      const candidateName = app.client?.user?.profile 
        ? `${app.client.user.profile.firstName} ${app.client.user.profile.lastName}`
        : 'Unknown Candidate'

      activities.push({
        id: app._id.toString(),
        type: 'application',
        title: 'New Application',
        description: `${candidateName} applied for ${app.job?.title || 'Unknown Position'}`,
        timestamp: app.createdAt,
        status: app.status === 'pending' ? 'new' : 'completed',
        jobId: app.job?._id?.toString(),
        candidateId: app.client?._id?.toString(),
        applicationId: app._id.toString()
      })
    })

    // Recent job postings
    const recentJobs = await Job.find({
      company: companyId
    })
    .sort({ createdAt: -1 })
    .limit(5)

    // Add job posting activities
    recentJobs.forEach(job => {
      activities.push({
        id: `job-${job._id.toString()}`,
        type: 'job_posted',
        title: 'Job Posted',
        description: `New job posting: ${job.title}`,
        timestamp: job.createdAt,
        status: 'completed',
        jobId: job._id.toString()
      })
    })

    // Recent status changes (interviews, hires, etc.)
    const statusChanges = await Application.find({
      company: companyId,
      status: { $in: ['interviewed', 'hired', 'offered'] }
    })
    .populate('job', 'title')
    .populate('client', 'user')
    .populate({
      path: 'client',
      populate: {
        path: 'user',
        select: 'profile.firstName profile.lastName'
      }
    })
    .sort({ updatedAt: -1 })
    .limit(5)

    // Add status change activities
    statusChanges.forEach(app => {
      const candidateName = app.client?.user?.profile 
        ? `${app.client.user.profile.firstName} ${app.client.user.profile.lastName}`
        : 'Unknown Candidate'

      let activityType = 'application'
      let title = 'Status Updated'
      let description = `${candidateName}'s application status changed`

      switch (app.status) {
        case 'interviewed':
          activityType = 'interview'
          title = 'Interview Scheduled'
          description = `Interview scheduled with ${candidateName} for ${app.job?.title || 'Unknown Position'}`
          break
        case 'hired':
          activityType = 'hire'
          title = 'Candidate Hired'
          description = `${candidateName} was hired for ${app.job?.title || 'Unknown Position'}`
          break
        case 'offered':
          title = 'Offer Extended'
          description = `Job offer extended to ${candidateName} for ${app.job?.title || 'Unknown Position'}`
          break
      }

      activities.push({
        id: `status-${app._id.toString()}`,
        type: activityType,
        title,
        description,
        timestamp: app.updatedAt,
        status: 'completed',
        jobId: app.job?._id?.toString(),
        candidateId: app.client?._id?.toString(),
        applicationId: app._id.toString()
      })
    })

    // Sort all activities by timestamp and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit)

    return NextResponse.json({
      success: true,
      data: sortedActivities
    })

  } catch (error) {
    console.error('Get company dashboard activity error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent activity' },
      { status: 500 }
    )
  }
}
