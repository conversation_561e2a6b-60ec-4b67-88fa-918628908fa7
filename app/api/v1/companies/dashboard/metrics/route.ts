import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Calculate time ranges based on timeRange parameter
    const now = new Date()
    let startDate: Date
    let previousStartDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 730 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
    }

    // Get metrics for current period
    const [
      currentJobs,
      currentApplications,
      currentHires,
      previousJobs,
      previousApplications,
      previousHires,
      applicationsByStatus,
      jobsByCategory,
      hiringFunnel,
      timeToHireData
    ] = await Promise.all([
      // Current period jobs
      Job.countDocuments({
        company: companyId,
        createdAt: { $gte: startDate }
      }),

      // Current period applications
      Application.countDocuments({
        company: companyId,
        createdAt: { $gte: startDate }
      }),

      // Current period hires
      Application.countDocuments({
        company: companyId,
        status: 'hired',
        updatedAt: { $gte: startDate }
      }),

      // Previous period jobs
      Job.countDocuments({
        company: companyId,
        createdAt: { $gte: previousStartDate, $lt: startDate }
      }),

      // Previous period applications
      Application.countDocuments({
        company: companyId,
        createdAt: { $gte: previousStartDate, $lt: startDate }
      }),

      // Previous period hires
      Application.countDocuments({
        company: companyId,
        status: 'hired',
        updatedAt: { $gte: previousStartDate, $lt: startDate }
      }),

      // Applications by status
      Application.aggregate([
        { $match: { company: companyId, createdAt: { $gte: startDate } } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),

      // Jobs by category
      Job.aggregate([
        { $match: { company: companyId, createdAt: { $gte: startDate } } },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]),

      // Hiring funnel
      Application.aggregate([
        { $match: { company: companyId, createdAt: { $gte: startDate } } },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            reviewed: { $sum: { $cond: [{ $ne: ['$status', 'pending'] }, 1, 0] } },
            interviewed: { $sum: { $cond: [{ $eq: ['$status', 'interviewed'] }, 1, 0] } },
            offered: { $sum: { $cond: [{ $eq: ['$status', 'offered'] }, 1, 0] } },
            hired: { $sum: { $cond: [{ $eq: ['$status', 'hired'] }, 1, 0] } }
          }
        }
      ]),

      // Time to hire calculation
      Application.find({
        company: companyId,
        status: 'hired',
        updatedAt: { $gte: startDate }
      }).select('createdAt updatedAt').limit(50)
    ])

    // Calculate growth percentages
    const jobsGrowth = previousJobs > 0 ? Math.round(((currentJobs - previousJobs) / previousJobs) * 100) : 0
    const applicationsGrowth = previousApplications > 0 ? Math.round(((currentApplications - previousApplications) / previousApplications) * 100) : 0
    const hiresGrowth = previousHires > 0 ? Math.round(((currentHires - previousHires) / previousHires) * 100) : 0

    // Calculate conversion rates
    const applicationToHireRate = currentApplications > 0 ? Math.round((currentHires / currentApplications) * 100) : 0
    const reviewRate = currentApplications > 0 ? Math.round(((currentApplications - (applicationsByStatus.find(s => s._id === 'pending')?.count || 0)) / currentApplications) * 100) : 0

    // Calculate average time to hire
    let averageTimeToHire = 0
    if (timeToHireData.length > 0) {
      const totalTime = timeToHireData.reduce((sum, app) => {
        const timeDiff = new Date(app.updatedAt).getTime() - new Date(app.createdAt).getTime()
        return sum + (timeDiff / (1000 * 60 * 60 * 24)) // Convert to days
      }, 0)
      averageTimeToHire = Math.round(totalTime / timeToHireData.length)
    }

    // Format applications by status
    const statusBreakdown = {
      pending: applicationsByStatus.find(s => s._id === 'pending')?.count || 0,
      reviewing: applicationsByStatus.find(s => s._id === 'reviewing')?.count || 0,
      interviewed: applicationsByStatus.find(s => s._id === 'interviewed')?.count || 0,
      offered: applicationsByStatus.find(s => s._id === 'offered')?.count || 0,
      hired: applicationsByStatus.find(s => s._id === 'hired')?.count || 0,
      rejected: applicationsByStatus.find(s => s._id === 'rejected')?.count || 0
    }

    // Format jobs by category
    const categoryBreakdown = jobsByCategory.reduce((acc, item) => {
      acc[item._id] = item.count
      return acc
    }, {} as Record<string, number>)

    // Format hiring funnel
    const funnel = hiringFunnel[0] || {
      total: 0,
      reviewed: 0,
      interviewed: 0,
      offered: 0,
      hired: 0
    }

    const metrics = {
      timeRange,
      period: {
        start: startDate.toISOString(),
        end: now.toISOString()
      },
      overview: {
        jobsPosted: currentJobs,
        applicationsReceived: currentApplications,
        candidatesHired: currentHires,
        averageTimeToHire,
        applicationToHireRate,
        reviewRate
      },
      growth: {
        jobs: jobsGrowth,
        applications: applicationsGrowth,
        hires: hiresGrowth
      },
      breakdown: {
        applicationsByStatus: statusBreakdown,
        jobsByCategory: categoryBreakdown
      },
      funnel: {
        applications: funnel.total,
        reviewed: funnel.reviewed,
        interviewed: funnel.interviewed,
        offered: funnel.offered,
        hired: funnel.hired,
        conversionRates: {
          applicationToReview: funnel.total > 0 ? Math.round((funnel.reviewed / funnel.total) * 100) : 0,
          reviewToInterview: funnel.reviewed > 0 ? Math.round((funnel.interviewed / funnel.reviewed) * 100) : 0,
          interviewToOffer: funnel.interviewed > 0 ? Math.round((funnel.offered / funnel.interviewed) * 100) : 0,
          offerToHire: funnel.offered > 0 ? Math.round((funnel.hired / funnel.offered) * 100) : 0
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: metrics
    })

  } catch (error) {
    console.error('Get company dashboard metrics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard metrics' },
      { status: 500 }
    )
  }
}
