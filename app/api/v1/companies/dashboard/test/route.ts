import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    console.log('=== DASHBOARD TEST ENDPOINT ===')
    console.log('Request URL:', request.url)
    console.log('Request headers:', Object.fromEntries(request.headers.entries()))
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    console.log('Auth result:', authResult)
    
    if (!authResult.success) {
      console.log('Authentication failed:', authResult.error)
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    console.log('Authenticated user ID:', userId)

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    console.log('User found:', user ? 'Yes' : 'No')
    console.log('User role:', user?.role)
    console.log('User companyId:', user?.companyId)
    
    if (!user) {
      console.log('User not found in database')
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    if (!user.companyId) {
      console.log('User has no company associated - creating test company')

      // Create a test company for the user
      const { Company } = await import('@/lib/models/company.model')
      const testCompany = new Company({
        name: 'Test Company',
        description: 'A test company for development',
        industry: 'Technology',
        website: 'https://testcompany.com',
        location: {
          city: 'San Francisco',
          state: 'CA',
          country: 'USA'
        },
        size: '11-50',
        createdBy: user._id,
        isActive: true,
        followersCount: 0,
        jobsCount: 0
      })

      await testCompany.save()

      // Update user's companyId
      await User.findByIdAndUpdate(user._id, { companyId: testCompany._id })

      // Refresh user data
      const updatedUser = await User.findById(userId).populate('companyId')

      console.log('Test company created:', testCompany._id)
      return NextResponse.json({
        success: true,
        data: {
          userId: updatedUser._id,
          userRole: updatedUser.role,
          companyId: testCompany._id,
          companyName: testCompany.name,
          message: 'Test company created and user updated'
        }
      })
    }

    console.log('=== SUCCESS ===')
    return NextResponse.json({
      success: true,
      data: {
        userId: user._id,
        userRole: user.role,
        companyId: user.companyId._id,
        companyName: user.companyId.name,
        message: 'Authentication and company access successful'
      }
    })

  } catch (error) {
    console.error('Dashboard test error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
