import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'

export async function POST(request: NextRequest) {
  console.log('=== COMPANY SETUP ENDPOINT ===')
  
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    console.log('Setting up company for user:', userId)

    // Check if user already has a company
    const user = await User.findById(userId).populate('companyId')
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    if (user.companyId) {
      console.log('User already has a company:', user.companyId._id)
      return NextResponse.json({
        success: true,
        message: 'Company already exists',
        data: {
          companyId: user.companyId._id,
          companyName: user.companyId.name
        }
      })
    }

    console.log('Creating test company for user...')

    // Generate a unique slug
    const baseSlug = 'test-company'
    const timestamp = Date.now()
    const slug = `${baseSlug}-${timestamp}`

    // Create a test company for the user
    const testCompany = new Company({
      name: 'Test Company',
      slug: slug,
      description: 'A test company for development and testing purposes',
      industry: ['Technology'],
      website: 'https://testcompany.com',
      locations: [{
        city: 'San Francisco',
        state: 'CA',
        country: 'USA',
        isHeadquarters: true
      }],
      contact: {
        email: user.email,
        phone: '******-0123'
      },
      size: 'small',
      createdBy: user._id,
      isActive: true,
      isFeatured: false
    })

    await testCompany.save()
    console.log('Test company created:', testCompany._id)

    // Update user's companyId
    await User.findByIdAndUpdate(user._id, { companyId: testCompany._id })
    console.log('User updated with company ID')

    return NextResponse.json({
      success: true,
      message: 'Test company created successfully',
      data: {
        companyId: testCompany._id,
        companyName: testCompany.name,
        userId: user._id,
        userEmail: user.email
      }
    })

  } catch (error) {
    console.error('Company setup error:', error)
    return NextResponse.json(
      { error: 'Failed to setup company', details: error.message },
      { status: 500 }
    )
  }
}
