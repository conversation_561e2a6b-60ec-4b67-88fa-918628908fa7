import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const jobId = searchParams.get('jobId')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Build query
    const query: any = {
      company: companyId
    }

    if (status) {
      query.status = status
    }

    if (jobId) {
      query.job = jobId
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Build sort object
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    // Get applications with populated data
    let applicationsQuery = Application.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('job', 'title department location type')
      .populate('client', 'user')
      .populate({
        path: 'client',
        populate: {
          path: 'user',
          select: 'profile.firstName profile.lastName email'
        }
      })

    // Add search functionality
    if (search) {
      // First get applications, then filter by candidate name or job title
      const allApplications = await Application.find(query)
        .populate('job', 'title department location type')
        .populate('client', 'user')
        .populate({
          path: 'client',
          populate: {
            path: 'user',
            select: 'profile.firstName profile.lastName email'
          }
        })

      const filteredApplications = allApplications.filter(app => {
        const candidateName = app.client?.user?.profile 
          ? `${app.client.user.profile.firstName} ${app.client.user.profile.lastName}`.toLowerCase()
          : ''
        const jobTitle = app.job?.title?.toLowerCase() || ''
        const email = app.client?.user?.email?.toLowerCase() || ''
        const searchLower = search.toLowerCase()

        return candidateName.includes(searchLower) || 
               jobTitle.includes(searchLower) || 
               email.includes(searchLower)
      })

      // Apply pagination to filtered results
      const paginatedFiltered = filteredApplications
        .sort((a, b) => {
          const aValue = sortBy === 'createdAt' ? new Date(a.createdAt).getTime() : a[sortBy]
          const bValue = sortBy === 'createdAt' ? new Date(b.createdAt).getTime() : b[sortBy]
          return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
        })
        .slice(skip, skip + limit)

      const applications = paginatedFiltered
      const total = filteredApplications.length

      // Format applications
      const formattedApplications = applications.map(app => ({
        _id: app._id,
        jobId: app.job?._id,
        jobTitle: app.job?.title,
        jobDepartment: app.job?.department,
        jobLocation: app.job?.location,
        jobType: app.job?.type,
        candidateName: app.client?.user?.profile 
          ? `${app.client.user.profile.firstName} ${app.client.user.profile.lastName}`
          : 'Unknown Candidate',
        candidateEmail: app.client?.user?.email,
        status: app.status,
        appliedAt: app.createdAt,
        lastUpdated: app.updatedAt,
        rating: app.rating,
        notes: app.notes,
        coverLetter: app.coverLetter,
        resume: app.resume
      }))

      return NextResponse.json({
        success: true,
        data: formattedApplications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNextPage: skip + applications.length < total,
          hasPrevPage: page > 1
        }
      })
    }

    // Regular query without search
    const applications = await applicationsQuery

    // Get total count for pagination
    const total = await Application.countDocuments(query)

    // Format applications
    const formattedApplications = applications.map(app => ({
      _id: app._id,
      jobId: app.job?._id,
      jobTitle: app.job?.title,
      jobDepartment: app.job?.department,
      jobLocation: app.job?.location,
      jobType: app.job?.type,
      candidateName: app.client?.user?.profile 
        ? `${app.client.user.profile.firstName} ${app.client.user.profile.lastName}`
        : 'Unknown Candidate',
      candidateEmail: app.client?.user?.email,
      status: app.status,
      appliedAt: app.createdAt,
      lastUpdated: app.updatedAt,
      rating: app.rating,
      notes: app.notes,
      coverLetter: app.coverLetter,
      resume: app.resume
    }))

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return NextResponse.json({
      success: true,
      data: formattedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    })

  } catch (error) {
    console.error('Get company applications error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company applications' },
      { status: 500 }
    )
  }
}

// Bulk update applications
export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { applicationIds, status, notes } = await request.json()

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Verify all applications belong to this company
    const applications = await Application.find({
      _id: { $in: applicationIds },
      company: companyId
    })

    if (applications.length !== applicationIds.length) {
      return NextResponse.json(
        { error: 'Some applications not found or access denied' },
        { status: 403 }
      )
    }

    // Update applications
    const updateData: any = { 
      status,
      updatedAt: new Date()
    }

    if (notes) {
      updateData.notes = notes
    }

    await Application.updateMany(
      { _id: { $in: applicationIds } },
      updateData
    )

    return NextResponse.json({
      success: true,
      message: `${applicationIds.length} applications updated successfully`
    })

  } catch (error) {
    console.error('Bulk update applications error:', error)
    return NextResponse.json(
      { error: 'Failed to update applications' },
      { status: 500 }
    )
  }
}
