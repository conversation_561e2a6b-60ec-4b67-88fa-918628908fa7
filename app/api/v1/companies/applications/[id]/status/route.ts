import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'
import { Job } from '@/lib/models/job.model'

interface RouteParams {
  params: {
    id: string
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id
    const { status, notes, reason, interviewData, offerData } = await request.json()

    // Validate status
    const validStatuses = ['pending', 'reviewing', 'interviewed', 'offered', 'hired', 'rejected']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      )
    }

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the application and verify ownership
    const application = await Application.findById(applicationId)
      .populate('job', 'title company')
      .populate('client', 'user')
      .populate({
        path: 'client',
        populate: {
          path: 'user',
          select: 'profile.firstName profile.lastName email'
        }
      })

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    if (application.company.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Application does not belong to your company' },
        { status: 403 }
      )
    }

    // Validate status transitions
    const currentStatus = application.status
    const validTransitions: Record<string, string[]> = {
      'pending': ['reviewing', 'rejected'],
      'reviewing': ['interviewed', 'offered', 'hired', 'rejected'],
      'interviewed': ['offered', 'hired', 'rejected', 'reviewing'], // Can go back for more review
      'offered': ['hired', 'rejected'],
      'hired': [], // Final state
      'rejected': [] // Final state
    }

    if (!validTransitions[currentStatus]?.includes(status)) {
      return NextResponse.json(
        { 
          error: `Invalid status transition from ${currentStatus} to ${status}`,
          validTransitions: validTransitions[currentStatus] || []
        },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = {
      status,
      updatedAt: new Date(),
      updatedBy: userId
    }

    // Add status-specific data
    switch (status) {
      case 'reviewing':
        updateData.reviewedAt = new Date()
        updateData.reviewedBy = userId
        if (notes) updateData.reviewNotes = notes
        break
        
      case 'interviewed':
        updateData.interviewedAt = new Date()
        if (interviewData) {
          updateData.interview = {
            ...interviewData,
            scheduledBy: userId,
            scheduledAt: new Date(interviewData.scheduledAt)
          }
        }
        break
        
      case 'offered':
        updateData.offeredAt = new Date()
        updateData.offeredBy = userId
        if (offerData) {
          updateData.offer = {
            ...offerData,
            offeredBy: userId,
            offeredAt: new Date()
          }
        }
        break
        
      case 'hired':
        updateData.hiredAt = new Date()
        updateData.hiredBy = userId
        if (notes) updateData.hireNotes = notes
        break
        
      case 'rejected':
        updateData.rejectedAt = new Date()
        updateData.rejectedBy = userId
        if (reason) updateData.rejectionReason = reason
        if (notes) updateData.rejectionNotes = notes
        break
    }

    // Add to status history
    if (!application.statusHistory) {
      application.statusHistory = []
    }
    
    application.statusHistory.push({
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason: reason || undefined,
      notes: notes || undefined,
      timestamp: new Date()
    })

    updateData.statusHistory = application.statusHistory

    // Update the application
    const updatedApplication = await Application.findByIdAndUpdate(
      applicationId,
      updateData,
      { new: true, runValidators: true }
    ).populate('job', 'title')
     .populate('client', 'user')
     .populate({
       path: 'client',
       populate: {
         path: 'user',
         select: 'profile.firstName profile.lastName email'
       }
     })

    // Update job statistics if hired or rejected
    if (status === 'hired' || status === 'rejected') {
      await Job.findByIdAndUpdate(application.job._id, {
        $inc: {
          [`stats.${status}Count`]: 1
        }
      })
    }

    // Log the status change
    console.log(`Application ${applicationId} status changed from ${currentStatus} to ${status} by user ${userId}`)

    // Prepare response data
    const responseData = {
      id: updatedApplication._id,
      status: updatedApplication.status,
      job: {
        id: updatedApplication.job._id,
        title: updatedApplication.job.title
      },
      candidate: {
        id: updatedApplication.client._id,
        name: `${updatedApplication.client.user.profile.firstName} ${updatedApplication.client.user.profile.lastName}`,
        email: updatedApplication.client.user.email
      },
      updatedAt: updatedApplication.updatedAt,
      statusHistory: updatedApplication.statusHistory
    }

    // Add status-specific data to response
    if (status === 'interviewed' && updatedApplication.interview) {
      responseData.interview = updatedApplication.interview
    }
    if (status === 'offered' && updatedApplication.offer) {
      responseData.offer = updatedApplication.offer
    }

    return NextResponse.json({
      success: true,
      message: `Application status updated to ${status}`,
      data: responseData
    })

  } catch (error) {
    console.error('Update application status error:', error)
    return NextResponse.json(
      { error: 'Failed to update application status' },
      { status: 500 }
    )
  }
}

// GET method to retrieve current status and valid transitions
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const applicationId = params.id

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find the application and verify ownership
    const application = await Application.findById(applicationId)
      .select('status statusHistory company')

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    if (application.company.toString() !== companyId.toString()) {
      return NextResponse.json(
        { error: 'Access denied. Application does not belong to your company' },
        { status: 403 }
      )
    }

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      'pending': ['reviewing', 'rejected'],
      'reviewing': ['interviewed', 'offered', 'hired', 'rejected'],
      'interviewed': ['offered', 'hired', 'rejected', 'reviewing'],
      'offered': ['hired', 'rejected'],
      'hired': [],
      'rejected': []
    }

    return NextResponse.json({
      success: true,
      data: {
        currentStatus: application.status,
        validTransitions: validTransitions[application.status] || [],
        statusHistory: application.statusHistory || []
      }
    })

  } catch (error) {
    console.error('Get application status error:', error)
    return NextResponse.json(
      { error: 'Failed to get application status' },
      { status: 500 }
    )
  }
}
