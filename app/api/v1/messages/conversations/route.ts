import { NextRequest, NextResponse } from 'next/server'
import { Message, Conversation } from '@/lib/models/message.model'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'

// GET /api/messages/conversations - Get user's conversations
async function getConversationsHandler(
  request: NextRequest,
  { user }: { user: any }
) {
  try {
    await connectDB()

    const url = new URL(request.url)
    const unreadOnly = url.searchParams.get('unreadOnly') === 'true'
    const dateFrom = url.searchParams.get('dateFrom')
    const dateTo = url.searchParams.get('dateTo')

    // Build query based on user role
    let query: any = {
      $or: [
        { 'participants.candidate._id': user._id },
        { 'participants.recruiter._id': user._id }
      ],
      status: 'active'
    }

    // Add date filters
    if (dateFrom || dateTo) {
      query.updatedAt = {}
      if (dateFrom) query.updatedAt.$gte = new Date(dateFrom)
      if (dateTo) query.updatedAt.$lte = new Date(dateTo)
    }

    // Get conversations
    let conversations = await Conversation.find(query)
      .populate('lastMessage')
      .sort({ updatedAt: -1 })
      .lean()

    // Filter by unread if requested
    if (unreadOnly) {
      conversations = conversations.filter(conv => conv.unreadCount > 0)
    }

    // Calculate total unread count
    const unreadCount = conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0)

    return NextResponse.json({
      conversations,
      unreadCount
    })

  } catch (error) {
    console.error('Get conversations error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/messages/conversations - Create new conversation
async function createConversationHandler(
  request: NextRequest,
  { user }: { user: any }
) {
  try {
    await connectDB()

    const { applicationId } = await request.json()

    // Find the application
    const application = await Application.findById(applicationId)
      .populate('job')
      .populate('userId')

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      )
    }

    // Get job and company details
    const job = await Job.findById(application.job._id).populate('company')
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check if conversation already exists
    const existingConversation = await Conversation.findOne({
      applicationId: applicationId
    })

    if (existingConversation) {
      return NextResponse.json({ conversation: existingConversation })
    }

    // Get candidate and recruiter info
    const candidate = await User.findById(application.userId)
    const recruiter = await User.findOne({ 
      companyId: job.company._id,
      role: { $in: ['recruiter', 'company_admin'] }
    })

    if (!candidate || !recruiter) {
      return NextResponse.json(
        { error: 'Unable to find conversation participants' },
        { status: 400 }
      )
    }

    // Create conversation
    const conversation = new Conversation({
      applicationId: applicationId,
      participants: {
        candidate: {
          _id: candidate._id,
          name: `${candidate.profile.firstName} ${candidate.profile.lastName}`,
          email: candidate.email
        },
        recruiter: {
          _id: recruiter._id,
          name: `${recruiter.profile.firstName} ${recruiter.profile.lastName}`,
          email: recruiter.email,
          companyId: recruiter.companyId
        }
      },
      job: {
        _id: job._id,
        title: job.title,
        company: {
          name: job.company.name,
          logo: job.company.logo
        }
      },
      status: 'active',
      unreadCount: 0
    })

    await conversation.save()

    // Create initial system message
    const initialMessage = new Message({
      conversationId: conversation._id,
      senderId: 'system',
      receiverId: 'system',
      content: `Conversation started for application to ${job.title} at ${job.company.name}`,
      type: 'system'
    })

    await initialMessage.save()

    // Update conversation with last message
    conversation.lastMessage = initialMessage._id
    await conversation.save()

    return NextResponse.json({
      conversation,
      message: 'Conversation created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Create conversation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getConversationsHandler)
export const POST = withAuth(createConversationHandler)
