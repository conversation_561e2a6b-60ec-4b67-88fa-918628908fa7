import { NextRequest } from 'next/server'
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>ethod } from '@/lib/api/route-handler'
import { adminService } from '@/lib/services/admin.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { CompanyFilters, DatabaseError } from '@/lib/types/api.types'

interface AdminCompanyFilters extends CompanyFilters {
  verificationStatus?: 'verified' | 'pending' | 'rejected'
  search?: string
}

// GET /api/v1/admin/companies - Get companies with verification status
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  
  // Pagination
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '20')
  
  // Validate pagination parameters
  if (page < 1 || limit < 1 || limit > 100) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100.',
      'pagination'
    )
  }
  
  // Admin-specific filters
  const filters: AdminCompanyFilters = {}
  
  const verificationStatus = searchParams.get('verificationStatus')
  const search = searchParams.get('search')
  const industry = searchParams.get('industry')
  const size = searchParams.get('size')
  const location = searchParams.get('location')
  const isActive = searchParams.get('isActive')
  const isFeatured = searchParams.get('isFeatured')
  
  if (verificationStatus && ['verified', 'pending', 'rejected'].includes(verificationStatus)) {
    filters.verificationStatus = verificationStatus as 'verified' | 'pending' | 'rejected'
  }
  
  if (search && search.trim()) {
    filters.search = search.trim()
  }
  
  if (industry) {
    filters.industry = industry.split(',').filter(Boolean)
  }
  
  if (size) {
    filters.size = size.split(',').filter(Boolean)
  }
  
  if (location && location.trim()) {
    filters.location = location.trim()
  }
  
  if (isActive !== null) {
    filters.isVerified = isActive === 'true'
  }
  
  if (isFeatured !== null) {
    filters.isFeatured = isFeatured === 'true'
  }
  
  const result = await adminService.getCompanies(page, limit, filters)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// POST /api/v1/admin/companies - Admin create company (if needed)
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  // This endpoint might not be needed as companies are typically created by users
  // But keeping it for admin purposes if needed
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'Company creation should be done through /api/v1/companies endpoint'
  )
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed. Use PUT /api/v1/admin/companies/[id] to update specific company.'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed. Use DELETE /api/v1/admin/companies/[id] to delete specific company.'
  )
}
