// app/(company-dashboard)/company-dashboard/notifications/page.tsx
"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Bell,
  Check,
  CheckCheck,
  Trash2,
  Filter,
  Search,
  Users,
  Briefcase,
  Calendar,
  Mail,
  AlertCircle,
  Info,
  TrendingUp,
  Settings,
  MoreHorizontal
} from "lucide-react"

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])

  const notifications = [
    {
      id: "1",
      type: "application",
      title: "New Application Received",
      message: "<PERSON> applied for Senior Frontend Developer position",
      timestamp: "2024-02-15T10:30:00Z",
      read: false,
      priority: "high",
      actionUrl: "/company-dashboard/applications/123",
      icon: Users,
      avatar: null
    },
    {
      id: "2",
      type: "job",
      title: "Job Posting Expiring Soon",
      message: "Your 'Backend Engineer' posting expires in 3 days",
      timestamp: "2024-02-15T09:15:00Z",
      read: false,
      priority: "medium",
      actionUrl: "/company-dashboard/jobs/456",
      icon: Briefcase,
      avatar: null
    },
    {
      id: "3",
      type: "interview",
      title: "Interview Scheduled",
      message: "Interview with John Doe scheduled for tomorrow at 2:00 PM",
      timestamp: "2024-02-14T16:45:00Z",
      read: true,
      priority: "high",
      actionUrl: "/company-dashboard/interviews/789",
      icon: Calendar,
      avatar: null
    },
    {
      id: "4",
      type: "system",
      title: "Weekly Report Available",
      message: "Your hiring analytics report for this week is ready",
      timestamp: "2024-02-14T08:00:00Z",
      read: true,
      priority: "low",
      actionUrl: "/company-dashboard/analytics",
      icon: TrendingUp,
      avatar: null
    },
    {
      id: "5",
      type: "application",
      title: "Application Status Updated",
      message: "Mike Chen's application moved to 'Interview Scheduled'",
      timestamp: "2024-02-13T14:20:00Z",
      read: true,
      priority: "medium",
      actionUrl: "/company-dashboard/applications/321",
      icon: Users,
      avatar: null
    },
    {
      id: "6",
      type: "team",
      title: "New Team Member Joined",
      message: "Alice Brown has accepted the invitation and joined your team",
      timestamp: "2024-02-13T11:30:00Z",
      read: true,
      priority: "medium",
      actionUrl: "/company-dashboard/team",
      icon: Users,
      avatar: null
    }
  ]

  const getFilteredNotifications = () => {
    switch (activeTab) {
      case "unread":
        return notifications.filter(n => !n.read)
      case "applications":
        return notifications.filter(n => n.type === "application")
      case "jobs":
        return notifications.filter(n => n.type === "job")
      case "interviews":
        return notifications.filter(n => n.type === "interview")
      default:
        return notifications
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "application": return Users
      case "job": return Briefcase
      case "interview": return Calendar
      case "team": return Users
      case "system": return Settings
      default: return Bell
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 48) return "Yesterday"
    return date.toLocaleDateString()
  }

  const handleSelectNotification = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    )
  }

  const handleSelectAll = () => {
    const filteredNotifications = getFilteredNotifications()
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id))
    }
  }

  const handleMarkAsRead = () => {
    console.log("Marking as read:", selectedNotifications)
    setSelectedNotifications([])
  }

  const handleDelete = () => {
    console.log("Deleting:", selectedNotifications)
    setSelectedNotifications([])
  }

  const unreadCount = notifications.filter(n => !n.read).length
  const filteredNotifications = getFilteredNotifications()

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
            <p className="text-muted-foreground mt-2">
              Stay updated with your hiring activities and important updates
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {selectedNotifications.length > 0 && (
              <>
                <Button variant="outline" onClick={handleMarkAsRead}>
                  <Check className="w-4 h-4 mr-2" />
                  Mark as Read ({selectedNotifications.length})
                </Button>
                <Button variant="outline" onClick={handleDelete}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </>
            )}
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Notification Stats */}
        <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{notifications.length}</div>
              <p className="text-xs text-muted-foreground">
                All time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unread</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{unreadCount}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {notifications.filter(n => n.type === 'application').length}
              </div>
              <p className="text-xs text-muted-foreground">
                New applications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Priority</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {notifications.filter(n => n.priority === 'high').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Urgent items
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Notifications Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between mb-6">
            <TabsList>
              <TabsTrigger value="all">
                All ({notifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread ({unreadCount})
              </TabsTrigger>
              <TabsTrigger value="applications">
                Applications ({notifications.filter(n => n.type === 'application').length})
              </TabsTrigger>
              <TabsTrigger value="jobs">
                Jobs ({notifications.filter(n => n.type === 'job').length})
              </TabsTrigger>
              <TabsTrigger value="interviews">
                Interviews ({notifications.filter(n => n.type === 'interview').length})
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                <CheckCheck className="w-4 h-4 mr-2" />
                {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>

          <TabsContent value={activeTab} className="w-full">
            <Card>
              <CardContent className="p-0">
                {filteredNotifications.length === 0 ? (
                  <div className="text-center py-12">
                    <Bell className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No notifications</h3>
                    <p className="text-muted-foreground">
                      {activeTab === 'unread' 
                        ? "You're all caught up! No unread notifications."
                        : `No ${activeTab === 'all' ? '' : activeTab} notifications to show.`
                      }
                    </p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredNotifications.map((notification) => {
                      const IconComponent = getTypeIcon(notification.type)
                      return (
                        <div 
                          key={notification.id} 
                          className={`flex items-start space-x-4 p-4 hover:bg-muted/50 transition-colors ${
                            !notification.read ? 'bg-blue-50/50' : ''
                          }`}
                        >
                          <Checkbox
                            checked={selectedNotifications.includes(notification.id)}
                            onCheckedChange={() => handleSelectNotification(notification.id)}
                            className="mt-1"
                          />

                          <div className="flex-shrink-0">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              notification.priority === 'high' ? 'bg-red-100' :
                              notification.priority === 'medium' ? 'bg-yellow-100' :
                              'bg-blue-100'
                            }`}>
                              <IconComponent className={`w-5 h-5 ${
                                notification.priority === 'high' ? 'text-red-600' :
                                notification.priority === 'medium' ? 'text-yellow-600' :
                                'text-blue-600'
                              }`} />
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h4 className={`text-sm font-medium ${!notification.read ? 'text-foreground' : 'text-muted-foreground'}`}>
                                    {notification.title}
                                  </h4>
                                  {!notification.read && (
                                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                  )}
                                  <Badge className={getPriorityColor(notification.priority)} variant="outline">
                                    {notification.priority}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <span>{formatTimestamp(notification.timestamp)}</span>
                                  <span className="capitalize">{notification.type}</span>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2 ml-4">
                                {notification.actionUrl && (
                                  <Button variant="outline" size="sm">
                                    View
                                  </Button>
                                )}
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Quick Actions */}
        {filteredNotifications.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Manage your notifications efficiently
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                <Button variant="outline">
                  <CheckCheck className="w-4 h-4 mr-2" />
                  Mark All as Read
                </Button>
                <Button variant="outline">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear Read Notifications
                </Button>
                <Button variant="outline">
                  <Settings className="w-4 h-4 mr-2" />
                  Notification Settings
                </Button>
                <Button variant="outline">
                  <Mail className="w-4 h-4 mr-2" />
                  Email Preferences
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
