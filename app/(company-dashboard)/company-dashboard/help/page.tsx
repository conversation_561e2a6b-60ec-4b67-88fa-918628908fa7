// app/(company-dashboard)/company-dashboard/help/page.tsx
"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  HelpCircle,
  Search,
  MessageCircle,
  Mail,
  Phone,
  Book,
  Video,
  FileText,
  Users,
  Briefcase,
  Settings,
  CreditCard,
  Shield,
  ExternalLink,
  Send,
  Clock,
  CheckCircle
} from "lucide-react"

export default function HelpPage() {
  const [activeTab, setActiveTab] = useState("faq")
  const [searchQuery, setSearchQuery] = useState("")
  const [contactForm, setContactForm] = useState({
    subject: "",
    category: "",
    message: "",
    priority: "medium"
  })

  const faqCategories = [
    {
      id: "getting-started",
      title: "Getting Started",
      icon: <Book className="w-5 h-5" />,
      questions: [
        {
          question: "How do I create my first job posting?",
          answer: "To create your first job posting, navigate to the Jobs section in your dashboard and click 'Create Job'. Fill in the job details including title, description, requirements, and publish when ready."
        },
        {
          question: "How do I invite team members?",
          answer: "Go to Team Management in your dashboard, click 'Invite Team Member', enter their email address, select their role, and send the invitation. They'll receive an email to join your organization."
        },
        {
          question: "What are the different user roles?",
          answer: "We offer four roles: Admin (full access), Recruiter (manage jobs and applications), Hiring Manager (review applications and interview), and Viewer (read-only access)."
        }
      ]
    },
    {
      id: "jobs",
      title: "Job Management",
      icon: <Briefcase className="w-5 h-5" />,
      questions: [
        {
          question: "How do I edit a published job?",
          answer: "Click on any job in your Jobs list, then click 'Edit Job'. You can modify all job details and republish. Changes will be reflected immediately on your job board."
        },
        {
          question: "Can I duplicate a job posting?",
          answer: "Yes, when viewing a job, click the 'More Actions' menu and select 'Duplicate Job'. This creates a copy that you can modify and publish as a new posting."
        },
        {
          question: "How long do job postings stay active?",
          answer: "Job postings remain active until you close them manually or they reach their expiration date (if set). You can extend or modify the duration at any time."
        }
      ]
    },
    {
      id: "applications",
      title: "Applications",
      icon: <Users className="w-5 h-5" />,
      questions: [
        {
          question: "How do I review applications?",
          answer: "Go to Applications in your dashboard to see all applications. Click on any application to view candidate details, resume, cover letter, and manage the application status."
        },
        {
          question: "Can I bulk update application statuses?",
          answer: "Yes, select multiple applications using the checkboxes and use the bulk actions menu to update statuses, send emails, or perform other actions on multiple applications at once."
        },
        {
          question: "How do I schedule interviews?",
          answer: "From an application page, click 'Schedule Interview', select the interview type (video, phone, in-person), set the date and time, and add any notes. The candidate will receive an automatic notification."
        }
      ]
    },
    {
      id: "billing",
      title: "Billing & Plans",
      icon: <CreditCard className="w-5 h-5" />,
      questions: [
        {
          question: "How do I upgrade my plan?",
          answer: "Go to Billing in your dashboard, select 'Plans & Pricing', choose your desired plan, and follow the upgrade process. Changes take effect immediately."
        },
        {
          question: "Can I cancel my subscription?",
          answer: "Yes, you can cancel anytime from your Billing settings. Your account will remain active until the end of your current billing period."
        },
        {
          question: "Do you offer refunds?",
          answer: "We offer prorated refunds for annual subscriptions cancelled within 30 days. Monthly subscriptions are not refundable but you can cancel to avoid future charges."
        }
      ]
    }
  ]

  const resources = [
    {
      title: "Getting Started Guide",
      description: "Complete walkthrough for new users",
      type: "guide",
      icon: <Book className="w-5 h-5" />,
      url: "#"
    },
    {
      title: "Video Tutorials",
      description: "Step-by-step video instructions",
      type: "video",
      icon: <Video className="w-5 h-5" />,
      url: "#"
    },
    {
      title: "API Documentation",
      description: "Technical documentation for developers",
      type: "docs",
      icon: <FileText className="w-5 h-5" />,
      url: "#"
    },
    {
      title: "Best Practices",
      description: "Tips for effective hiring",
      type: "guide",
      icon: <Users className="w-5 h-5" />,
      url: "#"
    }
  ]

  const supportTickets = [
    {
      id: "TICK-001",
      subject: "Unable to publish job posting",
      status: "open",
      priority: "high",
      created: "2024-02-15",
      lastUpdate: "2024-02-15"
    },
    {
      id: "TICK-002",
      subject: "Question about billing cycle",
      status: "resolved",
      priority: "medium",
      created: "2024-02-10",
      lastUpdate: "2024-02-12"
    }
  ]

  const filteredFAQs = faqCategories.map(category => ({
    ...category,
    questions: category.questions.filter(q =>
      searchQuery === "" ||
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.questions.length > 0)

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Contact form submitted:", contactForm)
    // Reset form
    setContactForm({
      subject: "",
      category: "",
      message: "",
      priority: "medium"
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in-progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Help & Support</h1>
            <p className="text-muted-foreground mt-2">
              Find answers, get help, and learn how to make the most of our platform
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Video className="w-4 h-4 mr-2" />
              Watch Tutorials
            </Button>
            <Button>
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-3 mb-8">
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="flex items-center space-x-4 p-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">Live Chat</h3>
                <p className="text-sm text-muted-foreground">Get instant help from our team</p>
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="flex items-center space-x-4 p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Mail className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Email Support</h3>
                <p className="text-sm text-muted-foreground">Send us a detailed message</p>
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="flex items-center space-x-4 p-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Phone className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold">Phone Support</h3>
                <p className="text-sm text-muted-foreground">Call us for urgent issues</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Help Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="faq">
              <HelpCircle className="w-4 h-4 mr-2" />
              FAQ
            </TabsTrigger>
            <TabsTrigger value="resources">
              <Book className="w-4 h-4 mr-2" />
              Resources
            </TabsTrigger>
            <TabsTrigger value="contact">
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Us
            </TabsTrigger>
            <TabsTrigger value="tickets">
              <FileText className="w-4 h-4 mr-2" />
              My Tickets
            </TabsTrigger>
          </TabsList>

          <TabsContent value="faq" className="w-full">
            <div className="w-full space-y-6">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search frequently asked questions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* FAQ Categories */}
              {filteredFAQs.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <HelpCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No results found</h3>
                    <p className="text-muted-foreground">
                      Try adjusting your search terms or browse our categories below
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-6">
                  {filteredFAQs.map((category) => (
                    <Card key={category.id}>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          {category.icon}
                          <span>{category.title}</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Accordion type="single" collapsible className="w-full">
                          {category.questions.map((faq, index) => (
                            <AccordionItem key={index} value={`${category.id}-${index}`}>
                              <AccordionTrigger className="text-left">
                                {faq.question}
                              </AccordionTrigger>
                              <AccordionContent className="text-muted-foreground">
                                {faq.answer}
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="resources" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2">
              {resources.map((resource, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        {resource.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold">{resource.title}</h3>
                          <ExternalLink className="w-4 h-4 text-muted-foreground" />
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {resource.description}
                        </p>
                        <Badge variant="outline" className="capitalize">
                          {resource.type}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="contact" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Send us a message</CardTitle>
                  <CardDescription>
                    We'll get back to you as soon as possible
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleContactSubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="subject">Subject</Label>
                      <Input
                        id="subject"
                        value={contactForm.subject}
                        onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="Brief description of your issue"
                        className="mt-1"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select value={contactForm.category} onValueChange={(value) => setContactForm(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="technical">Technical Issue</SelectItem>
                          <SelectItem value="billing">Billing Question</SelectItem>
                          <SelectItem value="feature">Feature Request</SelectItem>
                          <SelectItem value="account">Account Management</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select value={contactForm.priority} onValueChange={(value) => setContactForm(prev => ({ ...prev, priority: value }))}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="urgent">Urgent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="message">Message</Label>
                      <Textarea
                        id="message"
                        value={contactForm.message}
                        onChange={(e) => setContactForm(prev => ({ ...prev, message: e.target.value }))}
                        placeholder="Please provide as much detail as possible..."
                        className="mt-1"
                        rows={5}
                        required
                      />
                    </div>

                    <Button type="submit" className="w-full">
                      <Send className="w-4 h-4 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Other ways to reach us</CardTitle>
                  <CardDescription>
                    Choose the method that works best for you
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <MessageCircle className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Live Chat</h4>
                      <p className="text-sm text-muted-foreground">
                        Available 24/7 for immediate assistance
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <Mail className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Email</h4>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Phone className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Phone</h4>
                      <p className="text-sm text-muted-foreground">
                        +****************
                      </p>
                    </div>
                  </div>

                  <div className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-medium mb-2">Support Hours</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                      <p>Saturday: 10:00 AM - 4:00 PM EST</p>
                      <p>Sunday: Closed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tickets" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Support Tickets</CardTitle>
                <CardDescription>
                  Track your support requests and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                {supportTickets.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No support tickets</h3>
                    <p className="text-muted-foreground">
                      You haven't submitted any support tickets yet
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {supportTickets.map((ticket) => (
                      <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                            <FileText className="w-5 h-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium">{ticket.subject}</h4>
                            <p className="text-sm text-muted-foreground">
                              {ticket.id} • Created {new Date(ticket.created).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className={getStatusColor(ticket.status)}>
                            {ticket.status}
                          </Badge>
                          <Badge className={getPriorityColor(ticket.priority)} variant="outline">
                            {ticket.priority}
                          </Badge>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
