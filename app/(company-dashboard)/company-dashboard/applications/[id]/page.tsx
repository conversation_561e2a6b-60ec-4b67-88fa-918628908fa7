// app/(company-dashboard)/company-dashboard/applications/[id]/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter, useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useCompanyDashboardStore } from "@/stores/company-dashboard.store"
import {
  ArrowLeft,
  Download,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  Star,
  MessageSquare,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  ExternalLink,
  User,
  Briefcase,
  GraduationCap
} from "lucide-react"

export default function ApplicationDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const applicationId = params.id as string

  const {
    currentApplication,
    applicationLoading,
    fetchApplicationDetails,
    updateApplicationStatus,
    addApplicationNote
  } = useCompanyDashboardStore()

  const [activeTab, setActiveTab] = useState("overview")
  const [newNote, setNewNote] = useState("")
  const [isAddingNote, setIsAddingNote] = useState(false)

  useEffect(() => {
    if (applicationId) {
      fetchApplicationDetails(applicationId)
    }
  }, [applicationId, fetchApplicationDetails])

  const handleStatusUpdate = async (newStatus: string) => {
    if (currentApplication) {
      await updateApplicationStatus(currentApplication._id, newStatus)
    }
  }

  const handleAddNote = async () => {
    if (newNote.trim() && currentApplication) {
      setIsAddingNote(true)
      try {
        await addApplicationNote(currentApplication._id, newNote.trim())
        setNewNote("")
      } catch (error) {
        console.error('Failed to add note:', error)
      } finally {
        setIsAddingNote(false)
      }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'bg-blue-100 text-blue-800'
      case 'under_review': return 'bg-yellow-100 text-yellow-800'
      case 'interview_scheduled': return 'bg-purple-100 text-purple-800'
      case 'interviewed': return 'bg-indigo-100 text-indigo-800'
      case 'offer_extended': return 'bg-green-100 text-green-800'
      case 'hired': return 'bg-emerald-100 text-emerald-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted': return <FileText className="w-4 h-4" />
      case 'under_review': return <Eye className="w-4 h-4" />
      case 'interview_scheduled': return <Calendar className="w-4 h-4" />
      case 'interviewed': return <MessageSquare className="w-4 h-4" />
      case 'offer_extended': return <Star className="w-4 h-4" />
      case 'hired': return <CheckCircle className="w-4 h-4" />
      case 'rejected': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  if (applicationLoading || !currentApplication) {
    return (
      <div className="w-full h-full">
        <div className="w-full p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Applications
            </Button>
            <div className="flex items-center space-x-4">
              <Avatar className="w-12 h-12">
                <AvatarImage src={currentApplication.candidate?.avatar} />
                <AvatarFallback>
                  {currentApplication.candidate?.name?.split(' ').map(n => n[0]).join('') || 'CN'}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">
                  {currentApplication.candidate?.name || 'Anonymous Candidate'}
                </h1>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge className={getStatusColor(currentApplication.status)}>
                    <span className="flex items-center space-x-1">
                      {getStatusIcon(currentApplication.status)}
                      <span className="capitalize">{currentApplication.status.replace('_', ' ')}</span>
                    </span>
                  </Badge>
                  <span className="text-muted-foreground">
                    Applied {new Date(currentApplication.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download Resume
            </Button>
            <Button variant="outline">
              <Mail className="w-4 h-4 mr-2" />
              Send Email
            </Button>
            {currentApplication.status === 'submitted' && (
              <Button onClick={() => handleStatusUpdate('under_review')}>
                Start Review
              </Button>
            )}
            {currentApplication.status === 'under_review' && (
              <Button onClick={() => handleStatusUpdate('interview_scheduled')}>
                Schedule Interview
              </Button>
            )}
          </div>
        </div>

        {/* Application Details Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="resume">Resume & Documents</TabsTrigger>
            <TabsTrigger value="notes">Notes & Comments</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Candidate Information */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Candidate Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Email</p>
                          <p className="text-sm text-muted-foreground">
                            {currentApplication.candidate?.email || 'Not provided'}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <Phone className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Phone</p>
                          <p className="text-sm text-muted-foreground">
                            {currentApplication.candidate?.phone || 'Not provided'}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Location</p>
                          <p className="text-sm text-muted-foreground">
                            {currentApplication.candidate?.location || 'Not provided'}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <Briefcase className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Experience</p>
                          <p className="text-sm text-muted-foreground">
                            {currentApplication.candidate?.experience || 'Not specified'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Cover Letter</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {currentApplication.coverLetter || 'No cover letter provided'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Skills & Qualifications</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {currentApplication.candidate?.skills && currentApplication.candidate.skills.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {currentApplication.candidate.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No skills listed</p>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Application Status & Actions */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Application Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Current Status</span>
                      <Badge className={getStatusColor(currentApplication.status)}>
                        {currentApplication.status.replace('_', ' ')}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Applied For</span>
                      <span className="text-sm text-muted-foreground">
                        {currentApplication.job?.title || 'Unknown Position'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Application Date</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(currentApplication.createdAt).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Last Updated</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(currentApplication.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {currentApplication.status === 'submitted' && (
                      <Button 
                        className="w-full justify-start"
                        onClick={() => handleStatusUpdate('under_review')}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Start Review
                      </Button>
                    )}

                    {currentApplication.status === 'under_review' && (
                      <>
                        <Button 
                          className="w-full justify-start"
                          onClick={() => handleStatusUpdate('interview_scheduled')}
                        >
                          <Calendar className="w-4 h-4 mr-2" />
                          Schedule Interview
                        </Button>
                        <Button 
                          variant="outline"
                          className="w-full justify-start"
                          onClick={() => handleStatusUpdate('rejected')}
                        >
                          <XCircle className="w-4 h-4 mr-2" />
                          Reject Application
                        </Button>
                      </>
                    )}

                    {currentApplication.status === 'interviewed' && (
                      <>
                        <Button 
                          className="w-full justify-start"
                          onClick={() => handleStatusUpdate('offer_extended')}
                        >
                          <Star className="w-4 h-4 mr-2" />
                          Extend Offer
                        </Button>
                        <Button 
                          variant="outline"
                          className="w-full justify-start"
                          onClick={() => handleStatusUpdate('rejected')}
                        >
                          <XCircle className="w-4 h-4 mr-2" />
                          Reject Application
                        </Button>
                      </>
                    )}

                    {currentApplication.status === 'offer_extended' && (
                      <Button 
                        className="w-full justify-start"
                        onClick={() => handleStatusUpdate('hired')}
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Mark as Hired
                      </Button>
                    )}

                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Send Email
                    </Button>

                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Resume
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Candidate Rating</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2 mb-4">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          className="w-5 h-5 text-yellow-400 fill-current cursor-pointer hover:text-yellow-500" 
                        />
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Rate this candidate's overall fit for the position
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="resume" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Resume & Documents</CardTitle>
                <CardDescription>
                  View and download candidate's resume and supporting documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <FileText className="w-8 h-8 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">Resume.pdf</h4>
                          <p className="text-sm text-muted-foreground">Uploaded {new Date(currentApplication.createdAt).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Placeholder for additional documents */}
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No additional documents uploaded</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notes" className="w-full">
            <div className="w-full space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Add Note</CardTitle>
                  <CardDescription>
                    Add internal notes about this candidate
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="note">Note</Label>
                    <Textarea
                      id="note"
                      value={newNote}
                      onChange={(e) => setNewNote(e.target.value)}
                      placeholder="Add your thoughts about this candidate..."
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                  <Button 
                    onClick={handleAddNote}
                    disabled={!newNote.trim() || isAddingNote}
                  >
                    {isAddingNote ? 'Adding...' : 'Add Note'}
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notes History</CardTitle>
                  <CardDescription>
                    Internal notes and comments about this candidate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {currentApplication.notes && currentApplication.notes.length > 0 ? (
                    <div className="space-y-4">
                      {currentApplication.notes.map((note, index) => (
                        <div key={index} className="border-l-4 border-primary pl-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">{note.author || 'Unknown'}</span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(note.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">{note.content}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>No notes added yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Application Timeline</CardTitle>
                <CardDescription>
                  Track the progress of this application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">Application Submitted</h4>
                      <p className="text-sm text-muted-foreground">
                        {new Date(currentApplication.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {currentApplication.status !== 'submitted' && (
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Eye className="w-4 h-4 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">Under Review</h4>
                        <p className="text-sm text-muted-foreground">
                          Review started
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Add more timeline items based on status */}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
