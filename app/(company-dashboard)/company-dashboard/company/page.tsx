// app/(company-dashboard)/company-dashboard/company/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useCompanyStore } from "@/stores/company.store"
import { useAuthStore } from "@/stores/auth.store"
import {
  Building2,
  MapPin,
  Globe,
  Users,
  Calendar,
  Edit,
  Save,
  Upload,
  Eye,
  Star,
  Briefcase,
  Mail,
  Phone,
  ExternalLink
} from "lucide-react"

export default function CompanyProfilePage() {
  const router = useRouter()
  const { user } = useAuthStore()
  const {
    company,
    profileLoading,
    fetchCompanyProfile,
    updateCompanyProfile,
    uploadCompanyLogo
  } = useCompanyStore()

  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    website: "",
    location: "",
    industry: "",
    size: "",
    founded: "",
    email: "",
    phone: ""
  })

  useEffect(() => {
    fetchCompanyProfile()
  }, [fetchCompanyProfile])

  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || "",
        description: company.description || "",
        website: company.website || "",
        location: company.location || "",
        industry: company.industry || "",
        size: company.size || "",
        founded: company.founded || "",
        email: company.email || "",
        phone: company.phone || ""
      })
    }
  }, [company])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    try {
      await updateCompanyProfile(formData)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update company profile:', error)
    }
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      try {
        await uploadCompanyLogo(file)
      } catch (error) {
        console.error('Failed to upload logo:', error)
      }
    }
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Company Profile</h1>
            <p className="text-muted-foreground mt-2">
              Manage your company information and public profile
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={() => router.push('/companies')}>
              <Eye className="w-4 h-4 mr-2" />
              View Public Profile
            </Button>
            {isEditing ? (
              <>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </>
            ) : (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {/* Company Profile Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Company Details</TabsTrigger>
            <TabsTrigger value="branding">Branding</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Company Info Card */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>
                    Basic information about your company
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <Avatar className="w-16 h-16">
                      <AvatarImage src={company?.logo} />
                      <AvatarFallback>
                        <Building2 className="w-8 h-8" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold">{company?.name || 'Company Name'}</h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                        {company?.industry && (
                          <Badge variant="secondary">{company.industry}</Badge>
                        )}
                        {company?.location && (
                          <div className="flex items-center space-x-1">
                            <MapPin className="w-4 h-4" />
                            <span>{company.location}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="description">Company Description</Label>
                      {isEditing ? (
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          placeholder="Describe your company..."
                          className="mt-1"
                          rows={4}
                        />
                      ) : (
                        <p className="text-sm text-muted-foreground mt-1">
                          {company?.description || 'No description provided'}
                        </p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="website">Website</Label>
                        {isEditing ? (
                          <Input
                            id="website"
                            value={formData.website}
                            onChange={(e) => handleInputChange('website', e.target.value)}
                            placeholder="https://company.com"
                            className="mt-1"
                          />
                        ) : (
                          <div className="mt-1">
                            {company?.website ? (
                              <a 
                                href={company.website} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-sm text-primary hover:underline flex items-center space-x-1"
                              >
                                <Globe className="w-4 h-4" />
                                <span>{company.website}</span>
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            ) : (
                              <span className="text-sm text-muted-foreground">No website provided</span>
                            )}
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="industry">Industry</Label>
                        {isEditing ? (
                          <Input
                            id="industry"
                            value={formData.industry}
                            onChange={(e) => handleInputChange('industry', e.target.value)}
                            placeholder="Technology, Healthcare, etc."
                            className="mt-1"
                          />
                        ) : (
                          <p className="text-sm text-muted-foreground mt-1">
                            {company?.industry || 'Not specified'}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Company Stats</CardTitle>
                  <CardDescription>
                    Key metrics and information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Company Size</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.size || 'Not specified'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Founded</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.founded || 'Not specified'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Briefcase className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Active Jobs</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.activeJobsCount || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Eye className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Profile Views</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.profileViews || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Rating</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.rating ? `${company.rating}/5.0` : 'No ratings yet'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="details" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Information</CardTitle>
                <CardDescription>
                  Complete company details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Company Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        disabled={!isEditing}
                        placeholder="City, Country"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="size">Company Size</Label>
                      <Input
                        id="size"
                        value={formData.size}
                        onChange={(e) => handleInputChange('size', e.target.value)}
                        disabled={!isEditing}
                        placeholder="1-10, 11-50, 51-200, etc."
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="email">Contact Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        disabled={!isEditing}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="founded">Founded Year</Label>
                      <Input
                        id="founded"
                        value={formData.founded}
                        onChange={(e) => handleInputChange('founded', e.target.value)}
                        disabled={!isEditing}
                        placeholder="2020"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="branding" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Company Branding</CardTitle>
                <CardDescription>
                  Manage your company logo and visual identity
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Company Logo</Label>
                  <div className="mt-2 flex items-center space-x-4">
                    <Avatar className="w-20 h-20">
                      <AvatarImage src={company?.logo} />
                      <AvatarFallback>
                        <Building2 className="w-10 h-10" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                        id="logo-upload"
                      />
                      <Button 
                        variant="outline" 
                        onClick={() => document.getElementById('logo-upload')?.click()}
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Logo
                      </Button>
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended: Square image, at least 200x200px
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>
                  Manage your company profile visibility and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Public Profile</h4>
                      <p className="text-sm text-muted-foreground">
                        Make your company profile visible to job seekers
                      </p>
                    </div>
                    <Badge variant={company?.isPublic ? "default" : "secondary"}>
                      {company?.isPublic ? "Public" : "Private"}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Verification Status</h4>
                      <p className="text-sm text-muted-foreground">
                        Company verification status
                      </p>
                    </div>
                    <Badge variant={company?.isVerified ? "default" : "secondary"}>
                      {company?.isVerified ? "Verified" : "Pending"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
