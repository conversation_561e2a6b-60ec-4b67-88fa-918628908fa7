// app/(company-dashboard)/company-dashboard/jobs/[id]/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter, useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useCompanyDashboardStore } from "@/stores/company-dashboard.store"
import {
  ArrowLeft,
  Edit,
  Share2,
  Eye,
  Users,
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  Briefcase,
  MoreHorizontal,
  Download,
  MessageSquare,
  Star,
  TrendingUp
} from "lucide-react"

export default function JobDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const jobId = params.id as string

  const {
    currentJob,
    jobApplications,
    jobLoading,
    fetchJobDetails,
    fetchJobApplications,
    toggleJobStatus,
    deleteJob
  } = useCompanyDashboardStore()

  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    if (jobId) {
      fetchJobDetails(jobId)
      fetchJobApplications(jobId)
    }
  }, [jobId, fetchJobDetails, fetchJobApplications])

  const handleStatusToggle = async () => {
    if (currentJob) {
      const newStatus = currentJob.status === 'active' ? 'closed' : 'active'
      await toggleJobStatus(currentJob._id, newStatus)
    }
  }

  const handleDelete = async () => {
    if (currentJob && confirm('Are you sure you want to delete this job posting?')) {
      await deleteJob(currentJob._id)
      router.push('/company-dashboard/jobs')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (jobLoading || !currentJob) {
    return (
      <div className="w-full h-full">
        <div className="w-full p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Jobs
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{currentJob.title}</h1>
              <div className="flex items-center space-x-4 mt-2">
                <Badge className={getStatusColor(currentJob.status)}>
                  {currentJob.status}
                </Badge>
                <span className="text-muted-foreground">
                  Posted {new Date(currentJob.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push(`/company-dashboard/jobs/${jobId}/edit`)}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button 
              variant={currentJob.status === 'active' ? 'outline' : 'default'}
              onClick={handleStatusToggle}
            >
              {currentJob.status === 'active' ? 'Close Job' : 'Activate Job'}
            </Button>
          </div>
        </div>

        {/* Job Details Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="applications">
              Applications ({jobApplications.length})
            </TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Job Information */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Job Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {currentJob.description || 'No description provided'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {currentJob.requirements || 'No requirements specified'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Responsibilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <p className="text-muted-foreground whitespace-pre-wrap">
                        {currentJob.responsibilities || 'No responsibilities specified'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {currentJob.skills && currentJob.skills.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Required Skills</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {currentJob.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Job Stats & Info */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Job Statistics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Applications</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.applicationsCount || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Views</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.viewsCount || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Conversion Rate</span>
                      </div>
                      <span className="text-sm font-medium">
                        {currentJob.conversionRate || 0}%
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">Days Active</span>
                      </div>
                      <span className="text-sm font-medium">
                        {Math.floor((Date.now() - new Date(currentJob.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Job Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Location</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.location || 'Not specified'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Job Type</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.type || 'Not specified'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Experience Level</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.level || 'Not specified'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Department</span>
                      <span className="text-sm text-muted-foreground">
                        {currentJob.department || 'Not specified'}
                      </span>
                    </div>

                    {currentJob.salaryRange && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Salary Range</span>
                        <span className="text-sm text-muted-foreground">
                          {currentJob.salaryRange}
                        </span>
                      </div>
                    )}

                    {currentJob.applicationDeadline && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Application Deadline</span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(currentJob.applicationDeadline).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => router.push(`/company-dashboard/jobs/${jobId}/edit`)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Job
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={handleStatusToggle}
                    >
                      <Clock className="w-4 h-4 mr-2" />
                      {currentJob.status === 'active' ? 'Close Job' : 'Activate Job'}
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export Applications
                    </Button>
                    
                    <Button 
                      variant="destructive" 
                      className="w-full justify-start"
                      onClick={handleDelete}
                    >
                      <MoreHorizontal className="w-4 h-4 mr-2" />
                      Delete Job
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="applications" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Recent Applications</CardTitle>
                <CardDescription>
                  Latest applications for this job posting
                </CardDescription>
              </CardHeader>
              <CardContent>
                {jobApplications.length === 0 ? (
                  <div className="text-center py-12">
                    <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No applications yet</h3>
                    <p className="text-muted-foreground">
                      Applications will appear here when candidates apply to this job
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {jobApplications.slice(0, 10).map((application) => (
                      <div 
                        key={application._id} 
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer"
                        onClick={() => router.push(`/company-dashboard/applications/${application._id}`)}
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar>
                            <AvatarImage src={application.candidate?.avatar} />
                            <AvatarFallback>
                              {application.candidate?.name?.split(' ').map(n => n[0]).join('') || 'CN'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-medium">
                              {application.candidate?.name || 'Anonymous Candidate'}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Applied {new Date(application.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {application.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                    
                    {jobApplications.length > 10 && (
                      <div className="text-center pt-4">
                        <Button 
                          variant="outline"
                          onClick={() => router.push(`/company-dashboard/applications?job=${jobId}`)}
                        >
                          View All Applications ({jobApplications.length})
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.viewsCount || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Applications</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.applicationsCount || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    +5% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{currentJob.conversionRate || 0}%</div>
                  <p className="text-xs text-muted-foreground">
                    +2% from last week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Time to Apply</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2.4 days</div>
                  <p className="text-xs text-muted-foreground">
                    -0.5 days from last week
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Job Settings</CardTitle>
                <CardDescription>
                  Manage job posting settings and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Auto-close applications</h4>
                      <p className="text-sm text-muted-foreground">
                        Automatically close applications when deadline is reached
                      </p>
                    </div>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Email notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive email notifications for new applications
                      </p>
                    </div>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Public visibility</h4>
                      <p className="text-sm text-muted-foreground">
                        Job is visible on public job board
                      </p>
                    </div>
                    <Badge variant={currentJob.status === 'active' ? 'default' : 'secondary'}>
                      {currentJob.status === 'active' ? 'Visible' : 'Hidden'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
