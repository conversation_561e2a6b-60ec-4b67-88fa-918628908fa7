'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'
import { useCompanyDashboardStore } from '@/stores/company-dashboard.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { StatsCard, ActiveJobsCard, ApplicationsCard, PendingReviewsCard, InterviewsCard, HiredCandidatesCard, ResponseRateCard } from '@/components/dashboard/stats-card'
import { ActivityFeed, DashboardActivityFeed } from '@/components/dashboard/activity-feed'
import {
  Briefcase,
  Users,
  TrendingUp,
  Calendar,
  Plus,
  Eye,
  FileText,
  Building2,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  Star
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface QuickAction {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  variant: 'default' | 'secondary' | 'outline'
}

export default function CompanyDashboardPage() {
  const router = useRouter()
  const { user } = useAuthStore()
  const {
    stats,
    recentActivity,
    statsLoading,
    activityLoading,
    fetchDashboardStats,
    fetchRecentActivity
  } = useCompanyDashboardStore()
  const [activeTab, setActiveTab] = useState('overview')

  // Fetch dashboard data on component mount
  useEffect(() => {
    fetchDashboardStats()
    fetchRecentActivity()
  }, [])

  const quickActions: QuickAction[] = [
    {
      title: 'Post New Job',
      description: 'Create and publish a new job posting',
      icon: Plus,
      href: '/company-dashboard/jobs/create',
      variant: 'default'
    },
    {
      title: 'Review Applications',
      description: 'Review pending job applications',
      icon: FileText,
      href: '/company-dashboard/applications',
      variant: 'secondary'
    },
    {
      title: 'View Analytics',
      description: 'Check hiring metrics and performance',
      icon: BarChart3,
      href: '/company-dashboard/analytics',
      variant: 'outline'
    },
    {
      title: 'Company Profile',
      description: 'Update your company information',
      icon: Building2,
      href: '/company-dashboard/company',
      variant: 'outline'
    }
  ]



  const isLoading = statsLoading || activityLoading

  if (isLoading && !stats) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="w-full max-w-none grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Welcome back, {user?.profile?.firstName || 'there'}!
            </h1>
            <p className="text-muted-foreground mt-2">
              Here's what's happening with your hiring process today.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={() => router.push('/company-dashboard/company')}>
              <Building2 className="w-4 h-4 mr-2" />
              Company Profile
            </Button>
            <Button onClick={() => router.push('/company-dashboard/jobs/create')}>
              <Plus className="w-4 h-4 mr-2" />
              Post Job
            </Button>
          </div>
        </div>

        {/* Stats Cards - Full Width Grid */}
        <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <ActiveJobsCard
            value={stats?.activeJobs || 0}
            trend={stats?.jobsGrowth ? {
              value: stats.jobsGrowth,
              label: ' from last month',
              type: stats.jobsGrowth > 0 ? 'increase' : stats.jobsGrowth < 0 ? 'decrease' : 'neutral'
            } : undefined}
            loading={statsLoading}
          />

          <ApplicationsCard
            value={stats?.totalApplications || 0}
            trend={stats?.applicationsGrowth ? {
              value: stats.applicationsGrowth,
              label: '% from last month',
              type: stats.applicationsGrowth > 0 ? 'increase' : stats.applicationsGrowth < 0 ? 'decrease' : 'neutral'
            } : undefined}
            loading={statsLoading}
          />

          <PendingReviewsCard
            value={stats?.pendingReviews || 0}
            loading={statsLoading}
          />

          <InterviewsCard
            value={stats?.interviewsScheduled || 0}
            loading={statsLoading}
          />
        </div>

        {/* Main Content - Full Width Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activity">Recent Activity</TabsTrigger>
            <TabsTrigger value="actions">Quick Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Performance Metrics - Takes 2/3 width */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Hiring Performance</CardTitle>
                  <CardDescription>
                    Key metrics for your hiring process
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Response Rate</p>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold">{stats?.responseRate || 0}%</div>
                      <Badge variant="secondary">+5%</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Avg. Time to Hire</p>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold">{stats?.averageTimeToHire || 0} days</div>
                      <Badge variant="secondary">-2 days</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Profile Views</p>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold">{stats?.profileViews?.toLocaleString() || '0'}</div>
                      <Badge variant="secondary">+{stats?.profileViewsGrowth || 0}%</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Successful Hires</p>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold">{stats?.hiredCandidates || 0}</div>
                      <Badge variant="secondary">This month</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

              {/* Quick Stats - Takes 1/3 width */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Stats</CardTitle>
                  <CardDescription>
                    At a glance overview
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Job Fill Rate</span>
                    <span className="font-medium">{stats?.jobFillRate || 0}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Application Quality</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{stats?.applicationQuality || 0}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Active Recruiters</span>
                    <span className="font-medium">{stats?.activeRecruiters || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Open Positions</span>
                    <span className="font-medium">{stats?.activeJobs || 0}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="w-full">
            <DashboardActivityFeed
              activities={recentActivity}
              loading={activityLoading}
              onViewApplication={(applicationId) => router.push(`/company-dashboard/applications/${applicationId}`)}
              onViewJob={(jobId) => router.push(`/company-dashboard/jobs/${jobId}`)}
            />
          </TabsContent>

          <TabsContent value="actions" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2">
              {quickActions.map((action) => {
                const Icon = action.icon
                return (
                  <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Icon className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{action.title}</CardTitle>
                          <CardDescription>{action.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Button
                        variant={action.variant}
                        className="w-full"
                        onClick={() => router.push(action.href)}
                      >
                        Get Started
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
