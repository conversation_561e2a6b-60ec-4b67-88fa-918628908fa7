// app/(company-dashboard)/layout.tsx
"use client"

import React from "react"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { DashboardTopbar } from "@/components/dashboard/dashboard-topbar"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"
import { SidebarProvider } from "@/components/ui/sidebar"

interface CompanyDashboardLayoutProps {
  children: React.ReactNode
}

export default function CompanyDashboardLayout({
  children
}: CompanyDashboardLayoutProps) {
  return (
    <ProtectedRoute requiredRole="company_admin" fallbackPath="/login">
      <SidebarProvider>
        <div className="min-h-screen bg-background">
          <BackgroundPattern />

          {/* Redesigned Full-Width Dashboard Layout */}
          <div className="flex h-screen w-screen overflow-hidden">
            {/* Sidebar - Fixed Width */}
            <DashboardSidebar />

            {/* Main Content Area - Takes Remaining Space */}
            <div className="flex-1 flex flex-col min-w-0 w-full">
              {/* Top Navigation - Full Width */}
              <DashboardTopbar />

              {/* Content Area - Full Width, No Constraints */}
              <main className="flex-1 w-full overflow-auto">
                {children}
              </main>
            </div>
          </div>

          <NotificationSystem />
        </div>
      </SidebarProvider>
    </ProtectedRoute>
  )
}
