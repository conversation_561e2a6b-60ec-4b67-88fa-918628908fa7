import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'

export const metadata: Metadata = {
  title: {
    template: '%s | System Administration',
    default: 'Administration | JobPortal',
  },
  description: 'System administration dashboard for managing the job marketplace platform.',
}

export default function SystemAdministrationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ProtectedRoute requiredRole="admin" fallbackPath="/login">
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </ProtectedRoute>
  )
}
