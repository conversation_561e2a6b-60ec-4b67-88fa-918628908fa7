'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Building2, 
  Briefcase, 
  BarChart3,
  Settings,
  Shield,
  Database,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

export default function SystemAdministrationPage() {
  const router = useRouter()
  const { user } = useAuthStore()

  const adminModules = [
    {
      title: 'User Management',
      description: 'Manage all users, roles, and permissions',
      icon: Users,
      href: '/administration/users',
      stats: '1,234 users',
      status: 'active'
    },
    {
      title: 'Company Management',
      description: 'Oversee company registrations and profiles',
      icon: Building2,
      href: '/administration/companies',
      stats: '156 companies',
      status: 'active'
    },
    {
      title: 'Job Postings',
      description: 'Monitor and moderate job postings',
      icon: Briefcase,
      href: '/administration/jobs',
      stats: '2,341 jobs',
      status: 'active'
    },
    {
      title: 'Analytics & Reports',
      description: 'Platform-wide analytics and reporting',
      icon: BarChart3,
      href: '/administration/analytics',
      stats: 'Real-time data',
      status: 'active'
    },
    {
      title: 'System Settings',
      description: 'Configure platform settings and features',
      icon: Settings,
      href: '/administration/settings',
      stats: 'Configuration',
      status: 'active'
    },
    {
      title: 'Security & Audit',
      description: 'Security monitoring and audit logs',
      icon: Shield,
      href: '/administration/security',
      stats: 'Monitoring',
      status: 'active'
    },
    {
      title: 'Database Management',
      description: 'Database operations and maintenance',
      icon: Database,
      href: '/administration/database',
      stats: 'Healthy',
      status: 'active'
    },
    {
      title: 'System Health',
      description: 'Monitor system performance and health',
      icon: Activity,
      href: '/administration/health',
      stats: '99.9% uptime',
      status: 'active'
    }
  ]

  const systemAlerts = [
    {
      id: 1,
      type: 'warning',
      title: 'High Database Load',
      description: 'Database queries are taking longer than usual',
      timestamp: '2 hours ago'
    },
    {
      id: 2,
      type: 'info',
      title: 'Scheduled Maintenance',
      description: 'System maintenance scheduled for tonight at 2 AM',
      timestamp: '1 day ago'
    },
    {
      id: 3,
      type: 'success',
      title: 'Backup Completed',
      description: 'Daily backup completed successfully',
      timestamp: '6 hours ago'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return AlertTriangle
      case 'success': return CheckCircle
      case 'info': return Activity
      default: return Activity
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'text-yellow-600'
      case 'success': return 'text-green-600'
      case 'info': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            System Administration
          </h1>
          <p className="text-muted-foreground">
            Manage and monitor the job marketplace platform
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={() => router.push('/administration/settings')}>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button onClick={() => router.push('/administration/analytics')}>
            <BarChart3 className="w-4 h-4 mr-2" />
            View Analytics
          </Button>
        </div>
      </div>

      {/* System Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Companies</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Job Postings</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,341</div>
            <p className="text-xs text-muted-foreground">
              +15% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">99.9%</div>
            <p className="text-xs text-muted-foreground">
              Uptime this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Admin Modules */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Administration Modules</CardTitle>
              <CardDescription>
                Access different areas of system administration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {adminModules.map((module) => {
                  const Icon = module.icon
                  return (
                    <Card 
                      key={module.title} 
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => router.push(module.href)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <Icon className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                              <CardTitle className="text-sm">{module.title}</CardTitle>
                              <CardDescription className="text-xs">
                                {module.description}
                              </CardDescription>
                            </div>
                          </div>
                          <Badge className={getStatusColor(module.status)}>
                            {module.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm font-medium text-muted-foreground">
                          {module.stats}
                        </p>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Alerts */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>
                Recent system notifications and alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemAlerts.map((alert) => {
                  const Icon = getAlertIcon(alert.type)
                  return (
                    <div key={alert.id} className="flex items-start space-x-3">
                      <div className={`p-1.5 rounded-full ${getAlertColor(alert.type)}`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">{alert.title}</p>
                        <p className="text-xs text-muted-foreground">
                          {alert.description}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {alert.timestamp}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
