# Company Job and Recruitment Management Implementation Guide

## Overview

This guide outlines the complete implementation of the company dashboard system, covering all CRUD operations, API routes, services, and frontend integration for comprehensive job and recruitment management.

## Current Issues Identified

Based on the logs showing 404 errors:
- ✅ **FIXED**: `/api/v1/companies/dashboard/stats` - Authentication middleware issue resolved
- ✅ **FIXED**: `/api/v1/companies/dashboard/activity` - Authentication middleware issue resolved

## API Routes Architecture

### 1. Company Dashboard Core Routes

#### Dashboard Statistics
- **GET** `/api/v1/companies/dashboard/stats`
  - Returns comprehensive dashboard metrics
  - Metrics: active jobs, applications, pending reviews, interviews, hires, etc.
  - Growth calculations and trends

#### Dashboard Activity Feed
- **GET** `/api/v1/companies/dashboard/activity?limit=10`
  - Returns recent company activities
  - Activities: new applications, job postings, status changes, interviews, hires

#### Dashboard Metrics & Analytics
- **GET** `/api/v1/companies/dashboard/metrics?timeRange=30d`
  - Advanced analytics and reporting
  - Time-based metrics and comparisons
  - Performance indicators

### 2. Company Management Routes

#### Company Profile Management
- **GET** `/api/v1/companies/[id]` ✅ (Exists)
- **PUT** `/api/v1/companies/[id]` ✅ (Exists)
- **DELETE** `/api/v1/companies/[id]` ✅ (Exists)

#### Company Settings
- **GET** `/api/v1/companies/[id]/settings`
- **PUT** `/api/v1/companies/[id]/settings`

#### Company Verification
- **POST** `/api/v1/companies/[id]/verify`
- **GET** `/api/v1/companies/[id]/verification-status`

### 3. Job Management Routes

#### Job CRUD Operations
- **GET** `/api/v1/companies/jobs` ✅ (Exists)
- **POST** `/api/v1/companies/jobs` ✅ (Exists)
- **GET** `/api/v1/companies/jobs/[id]`
- **PUT** `/api/v1/companies/jobs/[id]`
- **DELETE** `/api/v1/companies/jobs/[id]`

#### Job Status Management
- **PUT** `/api/v1/companies/jobs/[id]/status`
- **PUT** `/api/v1/companies/jobs/[id]/publish`
- **PUT** `/api/v1/companies/jobs/[id]/unpublish`
- **PUT** `/api/v1/companies/jobs/[id]/close`

#### Job Analytics
- **GET** `/api/v1/companies/jobs/[id]/stats`
- **GET** `/api/v1/companies/jobs/[id]/analytics`

### 4. Application Management Routes

#### Application Overview
- **GET** `/api/v1/companies/applications` ✅ (Exists)
- **GET** `/api/v1/companies/applications/[id]`

#### Application Status Management
- **PUT** `/api/v1/companies/applications/[id]/status`
- **PUT** `/api/v1/companies/applications/[id]/review`
- **PUT** `/api/v1/companies/applications/[id]/interview`
- **PUT** `/api/v1/companies/applications/[id]/offer`
- **PUT** `/api/v1/companies/applications/[id]/hire`
- **PUT** `/api/v1/companies/applications/[id]/reject`

#### Bulk Operations
- **PUT** `/api/v1/companies/applications/bulk/status`
- **DELETE** `/api/v1/companies/applications/bulk/reject`

### 5. Team Management Routes

#### Team Members
- **GET** `/api/v1/companies/[id]/team`
- **POST** `/api/v1/companies/[id]/team`
- **PUT** `/api/v1/companies/[id]/team/[userId]`
- **DELETE** `/api/v1/companies/[id]/team/[userId]`

#### Recruiter Management
- **GET** `/api/v1/companies/[id]/recruiters`
- **POST** `/api/v1/companies/[id]/recruiters`
- **PUT** `/api/v1/companies/[id]/recruiters/[userId]`
- **DELETE** `/api/v1/companies/[id]/recruiters/[userId]`

#### Permissions & Roles
- **GET** `/api/v1/companies/[id]/permissions`
- **PUT** `/api/v1/companies/[id]/permissions/[userId]`

### 6. Interview Management Routes

#### Interview Scheduling
- **GET** `/api/v1/companies/interviews`
- **POST** `/api/v1/companies/interviews`
- **PUT** `/api/v1/companies/interviews/[id]`
- **DELETE** `/api/v1/companies/interviews/[id]`

#### Interview Feedback
- **POST** `/api/v1/companies/interviews/[id]/feedback`
- **GET** `/api/v1/companies/interviews/[id]/feedback`

### 7. Reporting & Analytics Routes

#### Company Reports
- **GET** `/api/v1/companies/reports/hiring`
- **GET** `/api/v1/companies/reports/performance`
- **GET** `/api/v1/companies/reports/diversity`

#### Export Functionality
- **GET** `/api/v1/companies/export/applications`
- **GET** `/api/v1/companies/export/jobs`
- **GET** `/api/v1/companies/export/reports`

## Required Services Implementation

### 1. Company Dashboard Service
```typescript
class CompanyDashboardService {
  async getDashboardStats(companyId: string): Promise<DashboardStats>
  async getRecentActivity(companyId: string, limit: number): Promise<Activity[]>
  async getMetrics(companyId: string, timeRange: string): Promise<Metrics>
  async getPerformanceData(companyId: string): Promise<Performance>
}
```

### 2. Enhanced Company Service
```typescript
class CompanyService {
  // Existing methods...
  async updateSettings(companyId: string, settings: CompanySettings): Promise<void>
  async verifyCompany(companyId: string): Promise<void>
  async getVerificationStatus(companyId: string): Promise<VerificationStatus>
  async getCompanyAnalytics(companyId: string): Promise<Analytics>
}
```

### 3. Enhanced Job Service
```typescript
class JobService {
  // Existing methods...
  async updateJobStatus(jobId: string, status: JobStatus): Promise<void>
  async publishJob(jobId: string): Promise<void>
  async unpublishJob(jobId: string): Promise<void>
  async closeJob(jobId: string): Promise<void>
  async getJobStats(jobId: string): Promise<JobStats>
  async getJobAnalytics(jobId: string): Promise<JobAnalytics>
}
```

### 4. Enhanced Application Service
```typescript
class ApplicationService {
  // Existing methods...
  async updateApplicationStatus(applicationId: string, status: ApplicationStatus): Promise<void>
  async scheduleInterview(applicationId: string, interviewData: InterviewData): Promise<void>
  async sendOffer(applicationId: string, offerData: OfferData): Promise<void>
  async hireCandidate(applicationId: string): Promise<void>
  async rejectApplication(applicationId: string, reason?: string): Promise<void>
  async bulkUpdateStatus(applicationIds: string[], status: ApplicationStatus): Promise<void>
}
```

### 5. Team Management Service
```typescript
class TeamManagementService {
  async getTeamMembers(companyId: string): Promise<TeamMember[]>
  async addTeamMember(companyId: string, memberData: TeamMemberData): Promise<void>
  async updateTeamMember(companyId: string, userId: string, updates: TeamMemberUpdates): Promise<void>
  async removeTeamMember(companyId: string, userId: string): Promise<void>
  async updatePermissions(companyId: string, userId: string, permissions: Permissions): Promise<void>
}
```

### 6. Interview Service
```typescript
class InterviewService {
  async scheduleInterview(interviewData: InterviewData): Promise<Interview>
  async updateInterview(interviewId: string, updates: InterviewUpdates): Promise<void>
  async cancelInterview(interviewId: string): Promise<void>
  async addFeedback(interviewId: string, feedback: InterviewFeedback): Promise<void>
  async getInterviews(companyId: string, filters?: InterviewFilters): Promise<Interview[]>
}
```

### 7. Reporting Service
```typescript
class ReportingService {
  async generateHiringReport(companyId: string, timeRange: string): Promise<HiringReport>
  async generatePerformanceReport(companyId: string): Promise<PerformanceReport>
  async generateDiversityReport(companyId: string): Promise<DiversityReport>
  async exportApplications(companyId: string, format: 'csv' | 'xlsx'): Promise<Buffer>
  async exportJobs(companyId: string, format: 'csv' | 'xlsx'): Promise<Buffer>
}
```

## Data Models & Types

### Dashboard Statistics
```typescript
interface DashboardStats {
  activeJobs: number
  totalApplications: number
  pendingReviews: number
  interviewsScheduled: number
  hiredCandidates: number
  profileViews: number
  responseRate: number
  averageTimeToHire: number
  jobFillRate: number
  applicationQuality: number
  activeRecruiters: number
  // Growth metrics
  jobsGrowth: number
  applicationsGrowth: number
  profileViewsGrowth: number
  hiredGrowth: number
}
```

### Activity Types
```typescript
interface Activity {
  id: string
  type: 'application' | 'job_posted' | 'interview' | 'hire' | 'status_change'
  title: string
  description: string
  timestamp: Date
  status: 'new' | 'completed' | 'pending'
  jobId?: string
  candidateId?: string
  applicationId?: string
  metadata?: Record<string, any>
}
```

## Implementation Priority

### Phase 1: Core Dashboard (IMMEDIATE)
1. ✅ Fix existing dashboard stats and activity routes
2. Implement missing dashboard metrics endpoint
3. Enhance job management endpoints
4. Implement application status management

### Phase 2: Advanced Features (NEXT)
1. Team management system
2. Interview scheduling and management
3. Advanced analytics and reporting
4. Bulk operations

### Phase 3: Enterprise Features (FUTURE)
1. Advanced permissions system
2. Custom reporting
3. Integration APIs
4. Advanced analytics

## Database Schema Enhancements

### Company Model Extensions
```typescript
// Add to existing Company model
interface ICompany {
  // ... existing fields
  settings: {
    allowPublicProfile: boolean
    showSalaryRanges: boolean
    autoRejectAfterDays?: number
    requireCoverLetter: boolean
    allowRemoteApplications: boolean
    emailNotifications: {
      newApplications: boolean
      interviewReminders: boolean
      statusUpdates: boolean
    }
    branding: {
      primaryColor?: string
      secondaryColor?: string
      customLogo?: string
    }
  }
  analytics: {
    lastAnalyticsUpdate: Date
    monthlyStats: {
      month: string
      applications: number
      hires: number
      views: number
    }[]
  }
}
```

### Interview Model
```typescript
interface IInterview {
  _id: ObjectId
  application: ObjectId // Reference to Application
  job: ObjectId // Reference to Job
  company: ObjectId // Reference to Company
  candidate: ObjectId // Reference to User (candidate)
  interviewer: ObjectId // Reference to User (interviewer)
  scheduledAt: Date
  duration: number // in minutes
  type: 'phone' | 'video' | 'in-person' | 'technical'
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show'
  location?: string
  meetingLink?: string
  notes?: string
  feedback?: {
    rating: number // 1-5
    comments: string
    skills: {
      technical: number
      communication: number
      cultural: number
    }
    recommendation: 'hire' | 'reject' | 'next-round'
    submittedBy: ObjectId
    submittedAt: Date
  }
  createdAt: Date
  updatedAt: Date
}
```

### Application Status History
```typescript
interface IApplicationStatusHistory {
  _id: ObjectId
  application: ObjectId
  fromStatus: ApplicationStatus
  toStatus: ApplicationStatus
  changedBy: ObjectId
  reason?: string
  notes?: string
  timestamp: Date
}
```

## Authentication & Authorization

### Role-Based Access Control
```typescript
// Company roles and permissions
const COMPANY_PERMISSIONS = {
  company_admin: [
    'manage_company',
    'manage_jobs',
    'manage_applications',
    'manage_team',
    'view_analytics',
    'manage_settings'
  ],
  recruiter: [
    'manage_jobs',
    'manage_applications',
    'view_analytics'
  ],
  hr_manager: [
    'manage_applications',
    'manage_team',
    'view_analytics'
  ]
}
```

### Middleware Implementation
```typescript
// Enhanced auth middleware for company routes
export async function requireCompanyAccess(
  request: NextRequest,
  companyId: string,
  requiredPermissions: string[] = []
): Promise<IUser> {
  const user = await authenticateRequest(request)

  // Admin can access any company
  if (user.role === 'admin') {
    return user
  }

  // Check if user belongs to the company
  if (!user.companyId || user.companyId.toString() !== companyId) {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'Access denied to this company'
    )
  }

  // Check specific permissions if required
  if (requiredPermissions.length > 0) {
    const userPermissions = COMPANY_PERMISSIONS[user.role] || []
    const hasPermission = requiredPermissions.every(
      permission => userPermissions.includes(permission)
    )

    if (!hasPermission) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        `Insufficient permissions. Required: ${requiredPermissions.join(', ')}`
      )
    }
  }

  return user
}
```

## Error Handling & Validation

### Custom Error Types
```typescript
export enum CompanyErrorCode {
  COMPANY_NOT_FOUND = 'COMPANY_NOT_FOUND',
  JOB_NOT_FOUND = 'JOB_NOT_FOUND',
  APPLICATION_NOT_FOUND = 'APPLICATION_NOT_FOUND',
  INVALID_STATUS_TRANSITION = 'INVALID_STATUS_TRANSITION',
  TEAM_MEMBER_EXISTS = 'TEAM_MEMBER_EXISTS',
  INTERVIEW_CONFLICT = 'INTERVIEW_CONFLICT',
  INSUFFICIENT_COMPANY_PERMISSIONS = 'INSUFFICIENT_COMPANY_PERMISSIONS'
}
```

### Validation Schemas
```typescript
// Job creation validation
export const createJobSchema = z.object({
  title: z.string().min(3).max(100),
  description: z.string().min(50).max(5000),
  requirements: z.array(z.string()).min(1),
  type: z.enum(['full-time', 'part-time', 'contract', 'internship']),
  level: z.enum(['entry', 'mid', 'senior', 'lead', 'executive']),
  category: z.string(),
  location: z.object({
    city: z.string(),
    state: z.string().optional(),
    country: z.string(),
    remote: z.boolean().default(false)
  }),
  salary: z.object({
    min: z.number().positive(),
    max: z.number().positive(),
    currency: z.string().default('USD'),
    period: z.enum(['hourly', 'monthly', 'yearly']).default('yearly')
  }).optional(),
  benefits: z.array(z.string()).optional(),
  skills: z.array(z.string()).min(1),
  experience: z.object({
    min: z.number().min(0),
    max: z.number().min(0)
  }).optional()
})

// Application status update validation
export const updateApplicationStatusSchema = z.object({
  status: z.enum(['pending', 'reviewing', 'interviewed', 'offered', 'hired', 'rejected']),
  notes: z.string().optional(),
  reason: z.string().optional(),
  interviewData: z.object({
    scheduledAt: z.string().datetime(),
    type: z.enum(['phone', 'video', 'in-person', 'technical']),
    duration: z.number().min(15).max(480),
    location: z.string().optional(),
    meetingLink: z.string().url().optional()
  }).optional()
})
```

## Caching Strategy

### Redis Cache Implementation
```typescript
class CompanyDashboardCache {
  private redis: Redis

  async getDashboardStats(companyId: string): Promise<DashboardStats | null> {
    const key = `company:${companyId}:dashboard:stats`
    const cached = await this.redis.get(key)
    return cached ? JSON.parse(cached) : null
  }

  async setDashboardStats(companyId: string, stats: DashboardStats): Promise<void> {
    const key = `company:${companyId}:dashboard:stats`
    await this.redis.setex(key, 300, JSON.stringify(stats)) // 5 minutes
  }

  async invalidateCompanyCache(companyId: string): Promise<void> {
    const pattern = `company:${companyId}:*`
    const keys = await this.redis.keys(pattern)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
}
```

## Performance Optimizations

### Database Indexing
```typescript
// Required indexes for optimal performance
const REQUIRED_INDEXES = [
  // Company indexes
  { collection: 'companies', index: { slug: 1 }, unique: true },
  { collection: 'companies', index: { 'stats.profileViews': -1 } },

  // Job indexes
  { collection: 'jobs', index: { company: 1, status: 1 } },
  { collection: 'jobs', index: { company: 1, createdAt: -1 } },
  { collection: 'jobs', index: { company: 1, isActive: 1 } },

  // Application indexes
  { collection: 'applications', index: { company: 1, status: 1 } },
  { collection: 'applications', index: { company: 1, createdAt: -1 } },
  { collection: 'applications', index: { job: 1, status: 1 } },

  // Interview indexes
  { collection: 'interviews', index: { company: 1, scheduledAt: 1 } },
  { collection: 'interviews', index: { interviewer: 1, scheduledAt: 1 } }
]
```

### Query Optimization
```typescript
// Optimized dashboard stats query
async function getDashboardStatsOptimized(companyId: string): Promise<DashboardStats> {
  const [
    activeJobs,
    applicationStats,
    interviewStats,
    companyStats
  ] = await Promise.all([
    // Active jobs count
    Job.countDocuments({ company: companyId, status: 'active', isActive: true }),

    // Application aggregation
    Application.aggregate([
      { $match: { company: new ObjectId(companyId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          recent: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] },
                1,
                0
              ]
            }
          }
        }
      }
    ]),

    // Interview stats
    Interview.aggregate([
      { $match: { company: new ObjectId(companyId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]),

    // Company profile views
    Company.findById(companyId).select('stats.profileViews')
  ])

  // Process and return formatted stats
  return processStatsData(activeJobs, applicationStats, interviewStats, companyStats)
}
```

## Next Steps

1. **Test Fixed Routes**: Verify the dashboard stats and activity endpoints work
2. **Implement Missing Routes**: Start with the most critical missing endpoints
3. **Enhance Services**: Add missing service methods
4. **Frontend Integration**: Update frontend to use new endpoints
5. **Testing**: Comprehensive testing of all CRUD operations

## Implementation Checklist

### Immediate (Phase 1)
- [x] Fix dashboard stats route authentication
- [x] Fix dashboard activity route authentication
- [ ] Implement dashboard metrics endpoint
- [ ] Add job status management endpoints
- [ ] Implement application status management
- [ ] Add basic team management

### Short Term (Phase 2)
- [ ] Interview scheduling system
- [ ] Advanced analytics endpoints
- [ ] Bulk operations for applications
- [ ] Enhanced reporting features
- [ ] Email notifications system

### Long Term (Phase 3)
- [ ] Advanced permissions system
- [ ] Custom dashboard widgets
- [ ] Integration APIs
- [ ] Advanced analytics and ML insights
- [ ] Multi-company management for enterprises
